#pragma once

#include <QObject>
#include <QFile>
#include <QDataStream>
#include <QDebug>
#include <QDateTime>
#include <QString>
#include <QFileDialog>
#include <QMessageBox>
#include <QtConcurrent/QtConcurrent> // 添加QtConcurrent头文件
#include <QTimer>
#include <QStorageInfo>
#include <QSettings>

#ifdef Q_OS_WIN
#include <windows.h>
#endif

class MeasureDataHandler : public QObject
{
    Q_OBJECT

public:
    // 版本定义
    enum FileVersion {
        VERSION_LEGACY = 0,  // 老版本，没有 dataArraySize
        VERSION_1 = 1,       // 第一个有版本号的版本，包含 dataArraySize
        VERSION_CURRENT = VERSION_1  // 当前版本
    };

    explicit MeasureDataHandler(QObject *parent = nullptr);

    // 文件信息
    unsigned int getErrorFlag() const { return errorFlag; }
    void setErrorFlag(unsigned int value) { errorFlag = value; }

    QString getIdent() const { return ident; }
    void setIdent(const QString &value) { ident = value; }

    QString getSoftwareVersion() const { return softwareVersion; }
    void setSoftwareVersion(const QString &value) { softwareVersion = value; }

    QString getHardwareVersion() const { return hardwareVersion; }
    void setHardwareVersion(const QString &value) { hardwareVersion = value; }

    QString getFileTime() const { return fileTime; }
    void setFileTime(const QString &value) { fileTime = value; }

    QString getFileChangeTime() const { return fileChangeTime; }
    void setFileChangeTime(const QString &value) { fileChangeTime = value; }

    QString getComment() const { return comment; }
    void setComment(const QString &value) { comment = value; }

    QString getMeasurementMode() const { return measurementMode; }
    void setMeasurementMode(const QString &value) { measurementMode = value; }

    unsigned int getDisplayLinLog() const { return displayLinLog; }
    void setDisplayLinLog(unsigned int value) { displayLinLog = value; }

    unsigned int getDisplayWaveAxisLower() const { return displayWaveAxisLower; }
    void setDisplayWaveAxisLower(unsigned int value) { displayWaveAxisLower = value; }

    unsigned int getDisplayWaveAxisUpper() const { return displayWaveAxisUpper; }
    void setDisplayWaveAxisUpper(unsigned int value) { displayWaveAxisUpper = value; }

    unsigned int getDisplayTimeAxisLower() const { return displayTimeAxisLower; }
    void setDisplayTimeAxisLower(unsigned int value) { displayTimeAxisLower = value; }

    unsigned int getDisplayTimeAxisUpper() const { return displayTimeAxisUpper; }
    void setDisplayTimeAxisUpper(unsigned int value) { displayTimeAxisUpper = value; }

    unsigned int getDisplayCountAxisLower() const { return displayCountAxisLower; }
    void setDisplayCountAxisLower(unsigned int value) { displayCountAxisLower = value; }

    unsigned int getDisplayCountAxisUpper() const { return displayCountAxisUpper; }
    void setDisplayCountAxisUpper(unsigned int value) { displayCountAxisUpper = value; }

    // 测量参数
    unsigned int getMonoValid() const { return monoValid; }
    void setMonoValid(unsigned int value) { monoValid = value; }

    unsigned int getMonoGrating() const { return monoGrating; }
    void setMonoGrating(unsigned int value) { monoGrating = value; }

    unsigned int getMonoGroove() const { return monoGroove; }
    void setMonoGroove(unsigned int value) { monoGroove = value; }

    unsigned int getMonoBlaze() const { return monoBlaze; }
    void setMonoBlaze(unsigned int value) { monoBlaze = value; }

    float getMonoWave() const { return monoWave; }
    void setMonoWave(float value) { monoWave = value; }

    unsigned int getMonoPort() const { return monoPort; }
    void setMonoPort(unsigned int value) { monoPort = value; }

    unsigned int getMonoShutter() const { return monoShutter; }
    void setMonoShutter(unsigned int value) { monoShutter = value; }

    QString getMonoFilter() const { return monoFilter; }
    void setMonoFilter(const QString &value) { monoFilter = value; }

    float getTdcResolution() const { return tdcResolution; }
    void setTdcResolution(float value) { tdcResolution = value; }

    unsigned int getWaveChannels() const { return waveChannels; }
    void setWaveChannels(unsigned int value) { waveChannels = value; }

    unsigned int getTimeChannels() const { return timeChannels; }
    void setTimeChannels(unsigned int value) { timeChannels = value; }

    std::vector<float> getTdcResolutionArray() const { return tdcResolutionArray; }
    void setTdcResolutionArray(const std::vector<float> &value) { tdcResolutionArray = value; }

    unsigned int getStopCondition() const { return stopCondition; }
    void setStopCondition(unsigned int value) { stopCondition = value; }

    unsigned int getMaxMeasurementTiming() const { return maxMeasurementTiming; }
    void setMaxMeasurementTiming(unsigned int value) { maxMeasurementTiming = value; }

    unsigned int getMaxNumPhotons() const { return maxNumPhotons; }
    void setMaxNumPhotons(unsigned int value) { maxNumPhotons = value; }

    unsigned long long getStartTime() const { return startTime; }
    void setStartTime(unsigned long long value) { startTime = value; }

    unsigned long long getEndTime() const { return endTime; }
    void setEndTime(unsigned long long value) { endTime = value; }

    // 获取测量持续时间（毫秒）
    qint64 getMeasurementDuration() const {
        if (startTime > 0 && endTime >= startTime) {
            return endTime - startTime;
        }
        // 如果测量仍在进行中，返回从开始到现在的时间
        if (startTime > 0 && saving) {
            return QDateTime::currentMSecsSinceEpoch() - startTime;
        }
        return 0; // 无效或未开始的测量
    }

    // 测量数据
    std::vector<float> getWaveAxisArray() const { return waveAxisArray; }
    void setWaveAxisArray(const std::vector<float> &value) { waveAxisArray = value; }

    std::vector<float> getTimeAxisArray() const { return timeAxisArray; }
    void setTimeAxisArray(const std::vector<float> &value) { timeAxisArray = value; }

    struct DataFrame {
        quint16 frameNumber;
        quint32 pulseCount;
        std::vector<std::vector<int>> data; //waveChannels x timeChannels

        // 重载[]运算符，方便访问data数组
        std::vector<int>& operator[](int index) {
            return data[index];
        }
    };
    QList<DataFrame> getDataArray() const { return dataArray; }
    void setDataArray(const QList<DataFrame> &value) { dataArray = value; }

    std::vector<std::vector<int>>& getDataArrayTotal() {
        return dataArrayTotal;
    }

    void setDataArrayTotal(std::vector<std::vector<int>> &&value) {
        dataArrayTotal = std::move(value);
    }

    bool saveProject(QDataStream &out, qint32 version = FileVersion::VERSION_CURRENT);
    bool openProject(QDataStream &in, qint32 version = FileVersion::VERSION_LEGACY);

    // 通过文件路径保存和打开项目
    bool saveProject();
    bool openProject(const QString &filePath);

public:
    void createSnapshot(int version);
    void restoreSnapshot(int version);
    qint64 calculateFileSize();
    void start(const QString &path);  // 现在使用.sflp格式
    void stop();

    bool isSaving() const { return saving; }
    void setIsSaving(bool value) { saving = value; }
    QString getFilePath() const { return filePath; }
    void setFilePath(const QString &value) { filePath = value; }

    // 新增方法：计算最佳FrameInFile值
    void calculateOptimalFrameInFile();

    // 新增方法：检查并保存数据（基于时间的保存策略）
    void checkAndSaveData();

    // 新增方法：获取系统可用内存（MB）
    qint64 getAvailableSystemMemory();
private:
    // 添加创建和追加DataFrame的方法
    void createAndAppendDataFrame(quint16 frameNumber, quint32 pulseCount, const std::vector<std::vector<int>> &data);

    void saveDataArrayToFile(); // 修改为异步调用
    void saveDataArrayToFileAsync(const QList<DataFrame> &dataArrayCopy); // 新增异步保存方法

    friend class ProjectFileManager;
    friend class SfdViewerMainWindow;

    // 成员变量
    QString filePath;
    bool saving;
    qint64 dataArraySize; // 记录dataArray的总大小
    int fileCounter = 1;
    int frameInFile; // 存储从配置文件中读取的帧数限制

    // 新增成员变量
    QTimer *m_saveTimer; // 定时保存计时器
    qint64 m_lastSaveTime; // 上次保存时间
    qint64 m_estimatedFrameSize; // 估算的单个数据帧大小

    // 文件信息
    unsigned int errorFlag;
    QString ident;
    QString softwareVersion;
    QString hardwareVersion;
    QString fileTime;
    QString fileChangeTime;
    QString comment;
    QString measurementMode;
    unsigned int displayLinLog;
    unsigned int displayWaveAxisLower;
    unsigned int displayWaveAxisUpper;
    unsigned int displayTimeAxisLower;
    unsigned int displayTimeAxisUpper;
    unsigned int displayCountAxisLower;
    unsigned int displayCountAxisUpper;

    // 测量参数
    unsigned int monoValid;
    unsigned int monoGrating;
    unsigned int monoGroove;
    unsigned int monoBlaze;
    float monoWave;
    unsigned int monoPort;
    unsigned int monoShutter;
    QString monoFilter;
    float tdcResolution;
    unsigned int waveChannels;
    unsigned int timeChannels;
    std::vector<float> tdcResolutionArray;
    unsigned int stopCondition;
    unsigned int maxMeasurementTiming;
    unsigned int maxNumPhotons;
    unsigned long long startTime;
    unsigned long long endTime;

    unsigned long fixedSize;

    // 测量数据
    std::vector<float> waveAxisArray;
    std::vector<float> timeAxisArray;
    QList<DataFrame> dataArray;
    std::vector<std::vector<int>> dataArrayTotal;  //waveChannels x timeChannels
};
