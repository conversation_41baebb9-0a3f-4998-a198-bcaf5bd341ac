# SpecFLIM架构评估结果

## 1. ProjectMetadata属性评估结果

### 1.1 m_currentVersion分析
**当前用途**: 版本控制和快照管理
**使用位置**:
- `createNewVersion()`: 递增版本号并创建快照
- `switchToVersion()`: 切换到指定版本
- `createNewProject()`: 初始化为0
- `openProject()`: 从metadata.sfp读取

**决策**: **保留**
- 虽然使用频率低，但对版本控制功能重要
- 在SFLP迁移后作为项目级元数据保存

### 1.2 m_lastDataFileNumber分析
**当前用途**: 测量文件序列号管理
**使用位置**:
- `startMeasurement()`: 递增并生成新文件名
- `openProject()`: 扫描现有文件更新最大值
- 硬编码文件名生成逻辑

**决策**: **移除，移交给FileNameManager**
**理由**:
1. FileNameManager已有`getNextProjectSequence()`等类似功能
2. 避免序列号管理职责分散
3. 统一文件命名策略

**实施建议**:
```cpp
// 移除ProjectFileManager中的硬编码
// 当前实现
QString fileName = AppConfig::instance()->readConfig("DataFileName", "SpecFlimData").toString()
    + "_" + QString::number(m_lastDataFileNumber) + ".sfd";

// 改为使用FileNameManager
QString fileName = FileNameManager::getInstance()->generateMeasurementFileName(m_projectPath);
```

### 1.3 简化后的ProjectMetadata
```cpp
struct ProjectMetadata {
    int currentVersion;  // 保留：版本控制功能
    QString projectName;
    QDateTime createdTime;
    QDateTime lastModifiedTime;
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};
```

## 2. 文件管理架构集成评估结果

### 2.1 当前集成状态

#### ✅ 已完全集成
**SflpFileManager**:
- 有完整的缓存管理（`m_sflpManagers`）
- 生命周期管理（`getSflpManager()`、`releaseSflpManager()`）
- 在数据读写中正确使用

#### ⚠️ 部分集成
**FileNameManager**:
- 在操作和分析数据保存中使用
- **缺失**: 测量数据文件名生成仍硬编码在ProjectFileManager中

#### ❌ 未集成
**FileRelationshipCache**:
- ProjectFileManager有重复的关系管理实现
- 存在功能重复：`m_childrenMap`、`m_parentMap`

**VirtualNodeManager**:
- 完全独立，未与ProjectFileManager集成
- 应该通过ProjectFileManager获取文件关系数据

**SflpFileOperations**:
- ProjectFileManager直接使用SflpFileManager
- 未利用SflpFileOperations的高级接口

### 2.2 具体集成缺陷

#### 缺陷1：文件关系管理重复
**问题代码**:
```cpp
// ProjectFileManager.h中的重复实现
QMap<QString, QStringList> m_childrenMap; // 父文件→子项列表
QMap<QString, QString> m_parentMap;       // 子项→父文件

void updateFileRelationship(const QString& parent, const QString& child) {
    QMutexLocker locker(&m_relationMutex);
    m_childrenMap[parent].append(child);
    m_parentMap[child] = parent;
}
```

**解决方案**:
```cpp
// 移除重复实现，使用FileRelationshipCache
FileRelationshipCache* m_relationshipCache;

void updateFileRelationship(const QString& parent, const QString& child) {
    m_relationshipCache->addRelationship(parent, child);
}
```

#### 缺陷2：SflpFileOperations未被利用
**问题代码**:
```cpp
// ProjectFileManager直接使用SflpFileManager
bool success = manager->writeDataSegment(segmentName, data);
```

**解决方案**:
```cpp
// 使用SflpFileOperations的高级接口
SflpFileOperations* m_sflpOperations;
bool success = m_sflpOperations->writeOperationData(sflpFileName, operationName, plotData);
```

#### 缺陷3：FileNameManager集成不完整
**问题代码**:
```cpp
// startMeasurement()中的硬编码
m_lastDataFileNumber++;
QString fileName = AppConfig::instance()->readConfig("DataFileName", "SpecFlimData").toString()
    + "_" + QString::number(m_lastDataFileNumber) + ".sfd";
```

**解决方案**:
```cpp
// 使用FileNameManager统一管理
QString fileName = m_fileNameManager->generateMeasurementFileName(m_projectPath);
```

## 3. 具体集成建议

### 3.1 ProjectFileManager重构步骤

#### 步骤1：移除重复的文件关系管理
```cpp
class ProjectFileManager : public QObject {
private:
    // 删除重复实现
    // QMap<QString, QStringList> m_childrenMap;
    // QMap<QString, QString> m_parentMap;
    // QMutex m_relationMutex;
    
    // 使用统一组件
    FileRelationshipCache* m_relationshipCache;
};
```

#### 步骤2：集成SflpFileOperations
```cpp
class ProjectFileManager : public QObject {
private:
    SflpFileOperations* m_sflpOperations;
    
public:
    bool saveOperationData(const QString& sflpFileName,
                          const OperationSequence& sequence,
                          const PlotDataCollection& plotData) {
        QString operationName = m_fileNameManager->generateOperationName(sflpFileName, sequence);
        return m_sflpOperations->writeOperationData(sflpFileName, operationName, plotData);
    }
};
```

#### 步骤3：完整集成FileNameManager
```cpp
class ProjectFileManager : public QObject {
private:
    // 移除m_lastDataFileNumber
    FileNameManager* m_fileNameManager;
    
    void startMeasurement() {
        QString fileName = m_fileNameManager->generateMeasurementFileName(m_projectPath);
        // 使用生成的文件名...
    }
};
```

### 3.2 VirtualNodeManager集成方案

#### ProjectFileManager提供数据接口
```cpp
class ProjectFileManager : public QObject {
public:
    QStringList getChildFiles(const QString& parentFile) const {
        return m_relationshipCache->getChildren(parentFile);
    }
    
    QString getParentFile(const QString& childFile) const {
        return m_relationshipCache->getParent(childFile);
    }

signals:
    void fileRelationshipChanged(const QString& parent, const QString& child);
    void fileAdded(const QString& fileName);
    void fileRemoved(const QString& fileName);
};
```

#### VirtualNodeManager连接ProjectFileManager
```cpp
class VirtualNodeManager : public QObject {
public:
    void connectToProjectManager(ProjectFileManager* projectManager) {
        connect(projectManager, &ProjectFileManager::fileRelationshipChanged,
                this, &VirtualNodeManager::onFileRelationshipChanged);
    }
};
```

## 4. 实施优先级

### 高优先级（第一阶段）
1. **移除m_lastDataFileNumber**，集成FileNameManager的测量文件命名
2. **移除重复的文件关系管理**，统一使用FileRelationshipCache
3. **简化ProjectMetadata结构**

### 中优先级（第二阶段）
1. **集成SflpFileOperations**，替代直接的SflpFileManager调用
2. **VirtualNodeManager与ProjectFileManager集成**

### 低优先级（第三阶段）
1. **性能优化和缓存策略调整**
2. **错误处理统一化**

## 5. 预期收益

### 架构简化
- 消除功能重复，减少维护复杂度
- 统一文件管理策略
- 清晰的职责分离

### 代码质量提升
- 减少硬编码，提高可配置性
- 统一的错误处理和日志记录
- 更好的测试覆盖率

### 维护性改善
- 单一职责原则的更好实现
- 组件间松耦合
- 更容易的功能扩展
