#include "SharedDataManager.h"
#include "ThemeManager.h"

SharedDataManager* SharedDataManager::instance = nullptr;

SharedDataManager* SharedDataManager::getInstance() {
    if (!instance) {
        instance = new SharedDataManager();
    }
    return instance;
}

SharedDataManager::SharedDataManager(QObject* parent) : QObject(parent) {
    // 初始化按Tab组织的数据结构
    // 为每个Tab初始化数据
    QVector<TabType> tabTypes = {TabType::Acquire, TabType::Process, TabType::Analysis};
    QVector<PlotDataType> plotTypes = {PlotDataType::FluorescenceMap, PlotDataType::DecayCurve, PlotDataType::SpectralCurve};
    QVector<DataOperationType> opTypes = {
        DataOperationType::Original,
        DataOperationType::Aligned,
        DataOperationType::Cropped,
        DataOperationType::AddedCurve,
        DataOperationType::FittedCurve
    };

    // 初始化每个标签页的数据
    for (TabType tab : tabTypes) {
        // TabData 构造函数已经初始化了时间戳
        m_tabData[tab] = TabData();

        // 初始化数据存储
        for (PlotDataType plotType : plotTypes) {
            for (DataOperationType opType : opTypes) {
                m_tabData[tab].dataByType[plotType][opType] = QVector<GraphData>();
            }
        }
    }
}

SharedDataManager::~SharedDataManager() {
    // 清理资源
}

void SharedDataManager::updateFluorescenceMapData(const ColorMapData& data, TabType tab) {
    m_tabData[tab].fluorescenceMapData = data;
    m_tabData[tab].lastUpdateTime[PlotDataType::FluorescenceMap] = QDateTime::currentDateTime();
    emit dataUpdated(tab, PlotDataType::FluorescenceMap);
}

void SharedDataManager::updateDecayCurveData(const QVector<GraphData>& data, const PlotSettings& settings, TabType tab) {
    // 保留拟合曲线
    QVector<GraphData> fitCurves = getData(TabType::Analysis, PlotDataType::DecayCurve, DataOperationType::FittedCurve);

    // 清除原始数据
    clearData(tab, PlotDataType::DecayCurve, DataOperationType::Original);

    // 添加新的原始数据
    for (const GraphData& graph : data) {
        if (graph.source == DataSource::Process) {
            GraphData originalGraph = graph;
            originalGraph.operationType = DataOperationType::Original;
            addData(tab, PlotDataType::DecayCurve, DataOperationType::Original, originalGraph);
        }
    }

    // 更新设置
    m_decayCurveSettings = settings;

    // 更新时间戳已在addData中处理
}

void SharedDataManager::updateSpectralCurveData(const QVector<GraphData>& data, const PlotSettings& settings, TabType tab) {
    // 保留拟合曲线
    QVector<GraphData> fitCurves = getData(TabType::Analysis, PlotDataType::SpectralCurve, DataOperationType::FittedCurve);

    // 清除原始数据
    clearData(tab, PlotDataType::SpectralCurve, DataOperationType::Original);

    // 添加新的原始数据
    for (const GraphData& graph : data) {
        if (graph.source == DataSource::Process) {
            GraphData originalGraph = graph;
            originalGraph.operationType = DataOperationType::Original;
            addData(tab, PlotDataType::SpectralCurve, DataOperationType::Original, originalGraph);
        }
    }

    // 更新设置
    m_spectralCurveSettings = settings;

    // 更新时间戳已在addData中处理
}

void SharedDataManager::addDecayCurveFit(const GraphData& fitData, TabType tab) {
    // 使用新接口添加拟合曲线
    addFitCurve(tab, PlotDataType::DecayCurve, fitData);
}

void SharedDataManager::addSpectralCurveFit(const GraphData& fitData, TabType tab) {
    // 使用新接口添加拟合曲线
    addFitCurve(tab, PlotDataType::SpectralCurve, fitData);
}

void SharedDataManager::clearAllFits(TabType tab) {
    clearDecayCurveFits(tab);
    clearSpectralCurveFits(tab);
}

void SharedDataManager::clearDecayCurveFits(TabType tab) {
    // 使用新接口清除拟合曲线
    clearFitCurves(tab, PlotDataType::DecayCurve);
}

void SharedDataManager::clearSpectralCurveFits(TabType tab) {
    // 使用新接口清除拟合曲线
    clearFitCurves(tab, PlotDataType::SpectralCurve);
}

void SharedDataManager::clearTabData(TabType tabType) {
    // 使用新接口清除数据
    switch (tabType) {
        case TabType::Acquire:
        {
            // 清除 Acquire 标签页相关的数据
            // 目前 Acquire 标签页没有特殊的数据需要清除
            // 如果将来有特殊数据，可以在这里添加
            break;
        }

        case TabType::Process:
        {
            // 清除 Process 标签页相关的数据
            clearData(tabType, PlotDataType::DecayCurve, DataOperationType::Original);
            clearData(tabType, PlotDataType::DecayCurve, DataOperationType::AddedCurve);
            clearData(tabType, PlotDataType::SpectralCurve, DataOperationType::Original);
            clearData(tabType, PlotDataType::SpectralCurve, DataOperationType::AddedCurve);

            break;
        }

        case TabType::Analysis:
        {
            // 清除 Analysis 标签页相关的数据
            clearFitCurves(tabType, PlotDataType::All);
            break;
        }

        default:
        {
            // 对于基础标签页或未知标签页，不做特殊处理
            break;
        }
    }
}

void SharedDataManager::clearAllData() {
    // 遍历所有标签页类型，调用 clearTabData
    clearTabData(TabType::Acquire);
    clearTabData(TabType::Process);
    clearTabData(TabType::Analysis);

    // 清除荧光图数据（这个在 clearTabData 中没有处理）
    for (auto tabIt = m_tabData.begin(); tabIt != m_tabData.end(); ++tabIt) {
        TabType tab = tabIt.key();
        tabIt.value().fluorescenceMapData = ColorMapData();
        tabIt.value().lastUpdateTime[PlotDataType::FluorescenceMap] = QDateTime::currentDateTime();

        // 为每个标签页发送更新信号
        emit dataUpdated(tab, PlotDataType::FluorescenceMap);
    }
}

ColorMapData SharedDataManager::getFluorescenceMapData(TabType tab) const {
    if (m_tabData.contains(tab)) {
        return m_tabData[tab].fluorescenceMapData;
    }
    return ColorMapData(); // 返回空数据
}

QVector<GraphData> SharedDataManager::getDecayCurveData(TabType tab) const {
    QVector<GraphData> result;

    // 添加指定标签页的数据
    // 添加原始数据
    result.append(getData(tab, PlotDataType::DecayCurve, DataOperationType::Original));

    // 添加对齐数据
    result.append(getData(tab, PlotDataType::DecayCurve, DataOperationType::Aligned));

    // 添加裁剪数据
    result.append(getData(tab, PlotDataType::DecayCurve, DataOperationType::Cropped));

    // 添加添加的曲线
    result.append(getData(tab, PlotDataType::DecayCurve, DataOperationType::AddedCurve));

    // 添加拟合曲线
    result.append(getData(tab, PlotDataType::DecayCurve, DataOperationType::FittedCurve));

    return result;
}

PlotSettings SharedDataManager::getDecayCurveSettings() const {
    return m_decayCurveSettings;
}

QVector<GraphData> SharedDataManager::getSpectralCurveData(TabType tab) const {
    QVector<GraphData> result;

    // 添加指定标签页的数据
    // 添加原始数据
    result.append(getData(tab, PlotDataType::SpectralCurve, DataOperationType::Original));

    // 添加对齐数据
    result.append(getData(tab, PlotDataType::SpectralCurve, DataOperationType::Aligned));

    // 添加裁剪数据
    result.append(getData(tab, PlotDataType::SpectralCurve, DataOperationType::Cropped));

    // 添加添加的曲线
    result.append(getData(tab, PlotDataType::SpectralCurve, DataOperationType::AddedCurve));

    // 添加拟合曲线
    result.append(getData(tab, PlotDataType::SpectralCurve, DataOperationType::FittedCurve));

    return result;
}

PlotSettings SharedDataManager::getSpectralCurveSettings() const {
    return m_spectralCurveSettings;
}

bool SharedDataManager::isDataUpdated(TabType tab, PlotDataType type) const {
    // 检查数据是否已更新
    if (m_tabData.contains(tab) &&
        m_tabData[tab].lastUpdateTime.contains(type) &&
        m_tabData[tab].lastSyncTime.contains(type)) {
        return m_tabData[tab].lastUpdateTime[type] > m_tabData[tab].lastSyncTime[type];
    }
    return false;
}

void SharedDataManager::markDataSynced(TabType tab, PlotDataType type) {
    // 标记数据已同步
    if (m_tabData.contains(tab)) {
        m_tabData[tab].lastSyncTime[type] = QDateTime::currentDateTime();
    }
}

// 添加对齐数据
void SharedDataManager::addAlignedData(PlotDataType type, const QVector<GraphData>& data, int alignmentValue, TabType tab) {
    // 清除之前的对齐数据
    clearData(tab, type, DataOperationType::Aligned);

    // 添加新的对齐数据
    for (const GraphData& graph : data) {
        GraphData alignedGraph = graph;
        alignedGraph.operationType = DataOperationType::Aligned;
        addData(tab, type, DataOperationType::Aligned, alignedGraph);
    }

    // 更新时间戳和发送信号已在addData中处理
}

// 添加曲线
void SharedDataManager::addCurve(PlotDataType type, const GraphData& curveData, TabType tab) {
    // 创建曲线数据
    GraphData curve = curveData;
    curve.operationType = DataOperationType::AddedCurve;

    // 添加到数据集
    addData(tab, type, DataOperationType::AddedCurve, curve);

    // 更新时间戳和发送信号已在addData中处理
}
void SharedDataManager::addData(TabType tab, PlotDataType plotType, DataOperationType opType, const GraphData& data) {
    // 数据验证
    if (!data.isValid()) {
        qWarning() << "SharedDataManager::addData - Invalid data, not adding";
        return;
    }

    // 添加数据到结构
    m_tabData[tab].dataByType[plotType][opType].append(data);

    // 更新时间戳
    if (plotType != PlotDataType::All) {
        m_tabData[tab].lastUpdateTime[plotType] = QDateTime::currentDateTime();

        // 发送信号
        emit dataUpdated(tab, plotType);
    } else {
        // 如果是All类型，更新所有类型的时间戳并发送信号
        m_tabData[tab].lastUpdateTime[PlotDataType::FluorescenceMap] = QDateTime::currentDateTime();
        m_tabData[tab].lastUpdateTime[PlotDataType::DecayCurve] = QDateTime::currentDateTime();
        m_tabData[tab].lastUpdateTime[PlotDataType::SpectralCurve] = QDateTime::currentDateTime();

        emit dataUpdated(tab, PlotDataType::FluorescenceMap);
        emit dataUpdated(tab, PlotDataType::DecayCurve);
        emit dataUpdated(tab, PlotDataType::SpectralCurve);
    }
}

// 获取数据
QVector<GraphData> SharedDataManager::getData(TabType tab, PlotDataType plotType, DataOperationType opType) const {
    // 检查是否存在指定类型的数据
    if (m_tabData.contains(tab) &&
        m_tabData[tab].dataByType.contains(plotType) &&
        m_tabData[tab].dataByType[plotType].contains(opType)) {

        // 获取数据
        QVector<GraphData> data = m_tabData[tab].dataByType[plotType][opType];

        // 如果是拟合曲线，添加日志记录颜色信息
        if (opType == DataOperationType::FittedCurve) {
            qDebug() << "SharedDataManager::getData - Getting fit curves for tab:" << static_cast<int>(tab)
                     << ", plot type:" << static_cast<int>(plotType);

            for (int i = 0; i < data.size(); ++i) {
                qDebug() << "SharedDataManager::getData - Fit curve[" << i << "] name: " << data[i].name
                         << ", color: " << data[i].pen.color().name();
            }
        }

        return data;
    }

    // 如果不存在，返回空数据
    return QVector<GraphData>();
}

// 清除数据
void SharedDataManager::clearData(TabType tab, PlotDataType plotType, DataOperationType opType) {
    if (plotType == PlotDataType::All) {
        // 清除所有类型的数据
        if (m_tabData.contains(tab)) {
            for (auto it = m_tabData[tab].dataByType.begin(); it != m_tabData[tab].dataByType.end(); ++it) {
                PlotDataType currentPlotType = it.key();
                if (m_tabData[tab].dataByType[currentPlotType].contains(opType)) {
                    m_tabData[tab].dataByType[currentPlotType][opType].clear();

                    // 更新时间戳
                    m_tabData[tab].lastUpdateTime[currentPlotType] = QDateTime::currentDateTime();

                    // 发送信号
                    emit dataUpdated(tab, currentPlotType);
                }
            }
        }
    } else {
        // 清除指定类型的数据
        if (m_tabData.contains(tab) &&
            m_tabData[tab].dataByType.contains(plotType) &&
            m_tabData[tab].dataByType[plotType].contains(opType)) {
            m_tabData[tab].dataByType[plotType][opType].clear();

            // 更新时间戳
            m_tabData[tab].lastUpdateTime[plotType] = QDateTime::currentDateTime();

            // 发送信号
            emit dataUpdated(tab, plotType);
        }
    }
}

// 数据是否存在
bool SharedDataManager::hasData(TabType tab, PlotDataType plotType, DataOperationType opType) const {
    return m_tabData.contains(tab) &&
           m_tabData[tab].dataByType.contains(plotType) &&
           m_tabData[tab].dataByType[plotType].contains(opType) &&
           !m_tabData[tab].dataByType[plotType][opType].isEmpty();
}

// 获取数据数量
int SharedDataManager::getDataCount(TabType tab, PlotDataType plotType, DataOperationType opType) const {
    if (m_tabData.contains(tab) &&
        m_tabData[tab].dataByType.contains(plotType) &&
        m_tabData[tab].dataByType[plotType].contains(opType)) {
        return m_tabData[tab].dataByType[plotType][opType].size();
    }
    return 0;
}

// 拟合曲线专用接口实现

// 添加拟合曲线
void SharedDataManager::addFitCurve(TabType tab, PlotDataType plotType, const GraphData& fitData) {
    // 数据验证
    if (!fitData.isValid()) {
        qWarning() << "SharedDataManager::addFitCurve - Invalid fit data, not adding";
        return;
    }

    // 确保操作类型为FittedCurve
    GraphData data = fitData;
    data.operationType = DataOperationType::FittedCurve;
    data.source = DataSource::Analysis;

    // 添加日志，记录添加到数据结构前的颜色
    qDebug() << "SharedDataManager::addFitCurve - Adding fit curve with color: "
             << data.pen.color().name() << " for curve: " << data.name;

    // 添加到结构
    m_tabData[tab].dataByType[plotType][DataOperationType::FittedCurve].append(data);

    // 更新时间戳
    if (plotType != PlotDataType::All) {
        m_tabData[tab].lastUpdateTime[plotType] = QDateTime::currentDateTime();

        // 发送信号
        emit dataUpdated(tab, plotType);
        emit fitCurvesChanged(tab, plotType);
    } else {
        // 如果是All类型，更新所有类型的时间戳并发送信号
        m_tabData[tab].lastUpdateTime[PlotDataType::DecayCurve] = QDateTime::currentDateTime();
        m_tabData[tab].lastUpdateTime[PlotDataType::SpectralCurve] = QDateTime::currentDateTime();

        emit dataUpdated(tab, PlotDataType::DecayCurve);
        emit dataUpdated(tab, PlotDataType::SpectralCurve);
        emit fitCurvesChanged(tab, PlotDataType::DecayCurve);
        emit fitCurvesChanged(tab, PlotDataType::SpectralCurve);
    }
}

// 从拟合结果直接创建并添加拟合曲线
void SharedDataManager::addFitCurveFromResult(
    TabType tab,
    PlotDataType plotType,
    const FitResult& result,
    const QString& modelName,
    const QColor& color
) {
    // 检查拟合结果是否有效
    if (!result.success || result.xData.isEmpty() || result.yData.isEmpty()) {
        qWarning() << "SharedDataManager::addFitCurveFromResult - Invalid fit result, not adding";
        return;
    }

    // 创建拟合曲线数据
    GraphData fitData;
    fitData.xData = result.xData.toVector();
    fitData.yData = result.yData.toVector();

    // 设置曲线名称
    int fitIndex = getDataCount(tab, plotType, DataOperationType::FittedCurve);
    fitData.name = QString("%1_%2_%3").arg(Constants::FIT_CURVE_IDENTIFIER).arg(modelName).arg(fitIndex + 1);

    // 设置样式
    QPen pen;
    QColor curveColor = color.isValid() ? color : ThemeManager::getFitCurveColor(fitIndex);
    pen.setColor(curveColor);
    pen.setWidth(2);
    pen.setStyle(Qt::SolidLine);
    fitData.pen = pen;

    // 添加日志，记录保存时的颜色
    qDebug() << "SharedDataManager::addFitCurveFromResult - Saving fit curve with color: "
             << curveColor.name() << " for curve: " << fitData.name;

    // 设置元数据
    fitData.isVisible = true;
    fitData.operationType = DataOperationType::FittedCurve;
    fitData.source = DataSource::Analysis;

    // 添加到数据存储
    addFitCurve(tab, plotType, fitData);

    // 同时存储原始拟合结果
    addFitResult(tab, plotType, result);
}

// 获取拟合曲线
QVector<GraphData> SharedDataManager::getFitCurves(TabType tab, PlotDataType plotType) const {
    return getData(tab, plotType, DataOperationType::FittedCurve);
}

// 更新拟合曲线
void SharedDataManager::updateFitCurve(TabType tab, PlotDataType plotType, int index, const GraphData& newData) {
    // 数据验证
    if (!newData.isValid()) {
        qWarning() << "SharedDataManager::updateFitCurve - Invalid fit data, not updating";
        return;
    }

    // 检查索引是否有效
    if (m_tabData.contains(tab) &&
        m_tabData[tab].dataByType.contains(plotType) &&
        m_tabData[tab].dataByType[plotType].contains(DataOperationType::FittedCurve) &&
        index >= 0 && index < m_tabData[tab].dataByType[plotType][DataOperationType::FittedCurve].size()) {

        // 确保操作类型为FittedCurve
        GraphData data = newData;
        data.operationType = DataOperationType::FittedCurve;
        data.source = DataSource::Analysis;

        // 更新数据
        m_tabData[tab].dataByType[plotType][DataOperationType::FittedCurve][index] = data;

        // 更新时间戳
        m_tabData[tab].lastUpdateTime[plotType] = QDateTime::currentDateTime();

        // 发送信号
        emit dataUpdated(tab, plotType);
    }
}

// 删除拟合曲线
void SharedDataManager::removeFitCurve(TabType tab, PlotDataType plotType, int index) {
    // 检查索引是否有效
    if (m_tabData.contains(tab) &&
        m_tabData[tab].dataByType.contains(plotType) &&
        m_tabData[tab].dataByType[plotType].contains(DataOperationType::FittedCurve) &&
        index >= 0 && index < m_tabData[tab].dataByType[plotType][DataOperationType::FittedCurve].size()) {

        // 删除数据
        m_tabData[tab].dataByType[plotType][DataOperationType::FittedCurve].removeAt(index);

        // 更新时间戳
        m_tabData[tab].lastUpdateTime[plotType] = QDateTime::currentDateTime();

        // 发送信号
        emit dataUpdated(tab, plotType);
    }
}

// 清除拟合曲线
void SharedDataManager::clearFitCurves(TabType tab, PlotDataType plotType) {
    if (plotType == PlotDataType::All) {
        // 清除所有类型的拟合曲线
        if (m_tabData.contains(tab)) {
            for (auto it = m_tabData[tab].dataByType.begin(); it != m_tabData[tab].dataByType.end(); ++it) {
                PlotDataType currentPlotType = it.key();
                if (m_tabData[tab].dataByType[currentPlotType].contains(DataOperationType::FittedCurve)) {
                    m_tabData[tab].dataByType[currentPlotType][DataOperationType::FittedCurve].clear();

                    // 同时清除拟合结果
                    clearFitResultsInternal(tab, currentPlotType);

                    // 更新时间戳
                    m_tabData[tab].lastUpdateTime[currentPlotType] = QDateTime::currentDateTime();

                    // 发送信号
                    emit dataUpdated(tab, currentPlotType);
                    emit fitCurvesChanged(tab, currentPlotType);
                }
            }
        }
    } else {
        // 清除指定类型的拟合曲线
        if (m_tabData.contains(tab) &&
            m_tabData[tab].dataByType.contains(plotType) &&
            m_tabData[tab].dataByType[plotType].contains(DataOperationType::FittedCurve)) {
            m_tabData[tab].dataByType[plotType][DataOperationType::FittedCurve].clear();

            // 同时清除拟合结果
            clearFitResultsInternal(tab, plotType);

            // 更新时间戳
            m_tabData[tab].lastUpdateTime[plotType] = QDateTime::currentDateTime();

            // 发送信号
            emit dataUpdated(tab, plotType);
            emit fitCurvesChanged(tab, plotType);
        }
    }
}

// 同步所有拟合曲线
void SharedDataManager::syncAllFitCurves(TabType sourceTab, TabType targetTab) {
    // 检查源标签页是否存在
    if (!m_tabData.contains(sourceTab)) {
        qWarning() << "SharedDataManager::syncAllFitCurves - Source tab not found";
        return;
    }

    // 遍历所有图表类型
    for (auto plotIt = m_tabData[sourceTab].dataByType.begin(); plotIt != m_tabData[sourceTab].dataByType.end(); ++plotIt) {
        PlotDataType plotType = plotIt.key();

        // 检查是否有拟合曲线
        if (m_tabData[sourceTab].dataByType[plotType].contains(DataOperationType::FittedCurve)) {
            // 清除目标标签页的拟合曲线
            clearFitCurves(targetTab, plotType);

            // 复制拟合曲线
            QVector<GraphData> fitCurves = m_tabData[sourceTab].dataByType[plotType][DataOperationType::FittedCurve];
            for (const GraphData& curve : fitCurves) {
                addFitCurve(targetTab, plotType, curve);
            }

            // 发送同步完成信号
            emit dataSynced(sourceTab, targetTab, plotType);
        }
    }
}

// 同步指定类型的所有数据
void SharedDataManager::syncPlotData(TabType sourceTab, TabType targetTab, PlotDataType plotType) {
    // 检查源标签页是否存在
    if (!m_tabData.contains(sourceTab) || !m_tabData[sourceTab].dataByType.contains(plotType)) {
        qWarning() << "SharedDataManager::syncPlotData - Source data not found";
        return;
    }

    // 清除目标标签页的数据（除了拟合曲线）
    for (auto opTypeIt = m_tabData[sourceTab].dataByType[plotType].begin();
         opTypeIt != m_tabData[sourceTab].dataByType[plotType].end();
         ++opTypeIt) {
        DataOperationType opType = opTypeIt.key();

        // 跳过拟合曲线，它们不应该从源标签页同步
        if (opType == DataOperationType::FittedCurve) {
            qDebug() << "SharedDataManager::syncPlotData - Skipping fit curves, they should not be synced from source tab";
            continue;
        }

        // 清除目标数据
        clearData(targetTab, plotType, opType);

        // 复制数据
        QVector<GraphData> data = opTypeIt.value();
        for (const GraphData& item : data) {
            addData(targetTab, plotType, opType, item);
        }
    }

    // 标记数据已同步
    markDataSynced(sourceTab, plotType);

    // 发送同步完成信号
    emit dataSynced(sourceTab, targetTab, plotType);
}

// 同步所有数据
void SharedDataManager::syncAllData(TabType sourceTab, TabType targetTab) {
    // 遍历所有图表类型
    for (auto plotIt = m_tabData[sourceTab].dataByType.begin(); plotIt != m_tabData[sourceTab].dataByType.end(); ++plotIt) {
        PlotDataType plotType = plotIt.key();
        syncPlotData(sourceTab, targetTab, plotType);
    }
}

// 数据同步接口实现

// 同步数据到目标Tab
void SharedDataManager::syncDataToTab(TabType sourceTab, TabType targetTab, PlotDataType plotType, DataOperationType opType) {
    // 检查源Tab是否有数据
    if (!hasData(sourceTab, plotType, opType)) {
        qDebug() << "SharedDataManager::syncDataToTab - Source tab has no data to sync";
        return;
    }

    // 获取源数据
    QVector<GraphData> sourceData = getData(sourceTab, plotType, opType);

    // 清除目标数据
    clearData(targetTab, plotType, opType);

    // 复制数据到目标Tab
    for (const GraphData& data : sourceData) {
        addData(targetTab, plotType, opType, data);
    }

    // 标记数据已同步
    markDataSynced(sourceTab, plotType);
}

// 拟合结果管理接口实现

// 添加拟合结果
void SharedDataManager::addFitResult(TabType tab, PlotDataType plotType, const FitResult& result) {
    // 数据验证
    if (!result.success) {
        qWarning() << "SharedDataManager::addFitResult - Invalid fit result, not adding";
        return;
    }

    // 确保标签页数据存在
    if (!m_tabData.contains(tab)) {
        m_tabData[tab] = TabData();
    }

    // 添加拟合结果
    m_tabData[tab].fitResults[plotType].append(result);

    // 更新时间戳
    m_tabData[tab].lastUpdateTime[plotType] = QDateTime::currentDateTime();

    // 发送信号
    emit dataUpdated(tab, plotType);
    emit fitCurvesChanged(tab, plotType);

    qDebug() << "SharedDataManager::addFitResult - Added fit result for tab:" << static_cast<int>(tab)
             << "plotType:" << static_cast<int>(plotType);
}

// 获取拟合结果
QVector<FitResult> SharedDataManager::getFitResults(TabType tab, PlotDataType plotType) const {
    if (m_tabData.contains(tab) && m_tabData[tab].fitResults.contains(plotType)) {
        return m_tabData[tab].fitResults[plotType];
    }
    return QVector<FitResult>();
}

// 清除拟合结果（内部方法，不发送信号）
void SharedDataManager::clearFitResultsInternal(TabType tab, PlotDataType plotType) {
    if (m_tabData.contains(tab) && m_tabData[tab].fitResults.contains(plotType)) {
        m_tabData[tab].fitResults[plotType].clear();
        qDebug() << "SharedDataManager::clearFitResultsInternal - Cleared fit results for tab:" << static_cast<int>(tab)
                 << "plotType:" << static_cast<int>(plotType);
    }
}

// 清除拟合结果
void SharedDataManager::clearFitResults(TabType tab, PlotDataType plotType) {
    clearFitResultsInternal(tab, plotType);

    // 更新时间戳
    if (m_tabData.contains(tab)) {
        m_tabData[tab].lastUpdateTime[plotType] = QDateTime::currentDateTime();
    }

    // 发送信号
    emit dataUpdated(tab, plotType);
    emit fitCurvesChanged(tab, plotType);
}

// 获取拟合结果数量
int SharedDataManager::getFitResultCount(TabType tab, PlotDataType plotType) const {
    if (m_tabData.contains(tab) && m_tabData[tab].fitResults.contains(plotType)) {
        return m_tabData[tab].fitResults[plotType].size();
    }
    return 0;
}
