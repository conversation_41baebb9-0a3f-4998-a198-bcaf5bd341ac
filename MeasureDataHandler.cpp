#include "MeasureDataHandler.h"
#include <QFile>
#include <QDataStream>
#include <QBuffer>
#include <QDateTime>
#include <QMessageBox>
#include <QDebug>
#include <QFileDialog>
#include <QtConcurrent/QtConcurrent> // 添加QtConcurrent头文件

qint64 MeasureDataHandler::calculateFileSize() {
    qint64 size = 0;
    if (fixedSize <= 0) {
        fixedSize = 0;

        // 计算基本类型成员变量的大小
        fixedSize += sizeof(saving);
        fixedSize += sizeof(errorFlag);
        fixedSize += ident.size() * sizeof(QChar);
        fixedSize += softwareVersion.size() * sizeof(QChar);
        fixedSize += hardwareVersion.size() * sizeof(QChar);
        fixedSize += fileTime.size() * sizeof(QChar);
        fixedSize += fileChangeTime.size() * sizeof(QChar);
        fixedSize += comment.size() * sizeof(QChar);
        fixedSize += measurementMode.size() * sizeof(QChar);
        fixedSize += sizeof(displayLinLog);
        fixedSize += sizeof(displayWaveAxisLower);
        fixedSize += sizeof(displayWaveAxisUpper);
        fixedSize += sizeof(displayTimeAxisLower);
        fixedSize += sizeof(displayTimeAxisUpper);
        fixedSize += sizeof(displayCountAxisLower);
        fixedSize += sizeof(displayCountAxisUpper);
        fixedSize += sizeof(monoValid);
        fixedSize += sizeof(monoGrating);
        fixedSize += sizeof(monoGroove);
        fixedSize += sizeof(monoBlaze);
        fixedSize += sizeof(monoWave);
        fixedSize += sizeof(monoPort);
        fixedSize += sizeof(monoShutter);
        fixedSize += monoFilter.size() * sizeof(QChar);
        fixedSize += sizeof(tdcResolution);
        fixedSize += sizeof(waveChannels);
        fixedSize += sizeof(timeChannels);
        fixedSize += sizeof(stopCondition);
        fixedSize += sizeof(maxMeasurementTiming);
        fixedSize += sizeof(maxNumPhotons);
        fixedSize += sizeof(startTime);
        fixedSize += sizeof(endTime);
        fixedSize += filePath.size() * sizeof(QChar);

        // 计算容器类型成员变量的大小
        fixedSize += tdcResolutionArray.size() * sizeof(float);
        fixedSize += waveAxisArray.size() * sizeof(float);
        fixedSize += timeAxisArray.size() * sizeof(float);
        fixedSize += dataArrayTotal.size() * waveChannels * timeChannels * sizeof(int);
    }
    size += fixedSize;
    size += dataArraySize * (waveChannels * timeChannels * sizeof(int) + sizeof(quint16) + sizeof(quint32));

    return size;
}
MeasureDataHandler::MeasureDataHandler(QObject *parent)
    : QObject(parent)
{
    fixedSize = -1;
    // 初始化默认值
    saving = false;
    errorFlag = 1;
    ident = "128-TCSPC";
    softwareVersion = "1.0";
    hardwareVersion = "1.0";
    fileTime = QDateTime::currentDateTime().toString("yyyy.MM.dd,hh:mm:ss");
    fileChangeTime = "";
    comment = "";
    measurementMode = "H1";
    displayLinLog = 1;
    displayWaveAxisLower = 0;
    displayWaveAxisUpper = 0;
    displayTimeAxisLower = 0;
    displayTimeAxisUpper = 0;
    displayCountAxisLower = 0;
    displayCountAxisUpper = 0;

    monoValid = 1;
    monoGrating = 1;
    monoGroove = 1800;
    monoBlaze = 500;
    monoWave = 621.3f;
    monoPort = 1;
    monoShutter = 1;
    monoFilter = "5:496LP";
    tdcResolution = 10.0f;
    waveChannels = 128;
    timeChannels = 4096;
    stopCondition = 3;
    maxMeasurementTiming = 20;
    maxNumPhotons = 1000;
    startTime = 0;
    endTime = 0;

    // 初始化vector和QList
    tdcResolutionArray.resize(waveChannels, 0.0f);
    waveAxisArray.resize(waveChannels, 0.0f);
    timeAxisArray.resize(timeChannels, 0.0f);
    dataArrayTotal.resize(waveChannels, std::vector<int>(timeChannels, 0));
    dataArray.clear();
    filePath = ""; // 初始化文件路径为空
    dataArraySize = 0; // 初始化dataArraySize为0

    // 初始化新增成员变量
    m_lastSaveTime = QDateTime::currentMSecsSinceEpoch();

    // 创建并启动定时保存计时器
    m_saveTimer = new QTimer(this);
    connect(m_saveTimer, &QTimer::timeout, this, &MeasureDataHandler::checkAndSaveData);
    m_saveTimer->start(30000); // 每30秒检查一次

    // 估算单个数据帧大小
    m_estimatedFrameSize = sizeof(DataFrame) + (waveChannels * timeChannels * sizeof(int));

    // 直接计算最佳FrameInFile值，不再依赖配置文件
    calculateOptimalFrameInFile();
}

void MeasureDataHandler::createSnapshot(int version)
{
    // 创建快照文件
    QString snapshotPath = QString("snapshot_%1.dat").arg(version);
    QFile file(snapshotPath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        QDataStream out(&file);
        serializeToStream(out);
        file.close();
    }
}

void MeasureDataHandler::restoreSnapshot(int version)
{
    // 恢复快照文件
    QString snapshotPath = QString("snapshot_%1.dat").arg(version);
    QFile file(snapshotPath);
    if (file.open(QIODevice::ReadOnly)) {
        QDataStream in(&file);
        deserializeFromStream(in);
        file.close();
    }
}

bool MeasureDataHandler::serializeToStream(QDataStream &out, qint32 version) const
{
    // 注意：stop()方法不能在const方法中调用，序列化应该不改变对象状态
    if (!out.device() || !out.device()->isWritable()) {
        qCritical() << "Save project failed: device not writable";
        return false;
    }

    // 增加边界检查
    if (tdcResolutionArray.size() != waveChannels) {
        qCritical() << "TDC resolution array size mismatch with wave channels";
        return false;
    }
    if (waveAxisArray.size() != waveChannels) {
        qCritical() << "Wave axis array size mismatch with wave channels";
        return false;
    }
    if (timeAxisArray.size() != timeChannels) {
        qCritical() << "Time axis array size mismatch with time channels";
        return false;
    }

    // 注意：在const方法中不能修改成员变量，使用临时变量
    QString currentFileChangeTime = QDateTime::currentDateTime().toString("yyyy.MM.dd,hh:mm:ss");

    // 保存基本信息 (所有版本共有)
    out << errorFlag << ident << softwareVersion << hardwareVersion << fileTime << currentFileChangeTime << comment << measurementMode << displayLinLog << displayWaveAxisLower << displayWaveAxisUpper << displayTimeAxisLower << displayTimeAxisUpper << displayCountAxisLower << displayCountAxisUpper;
    if(out.status() != QDataStream::Ok) {
        qCritical() << "File header serialization failed";
        return false;
    }

    // 保存测量参数 (所有版本共有)
    out << monoValid << monoGrating << monoGroove << monoBlaze << monoWave << monoPort << monoShutter << monoFilter << tdcResolution << waveChannels << timeChannels;
    for (int i = 0; i < static_cast<int>(waveChannels); ++i) {
        if (i >= 0 && i < tdcResolutionArray.size()) {
            out << tdcResolutionArray[i];
            if(out.status() != QDataStream::Ok) {
                qCritical() << "TDC resolution serialization failed at index" << i;
                return false;
            }
        } else {
            qCritical() << "Index out of bounds in tdcResolutionArray: " << i;
            return false;
        }
    }

    // 保存停止条件 (所有版本共有)
    out << stopCondition << maxMeasurementTiming << maxNumPhotons << startTime << endTime;
    if(out.status() != QDataStream::Ok) {
        qCritical() << "Measurement parameters serialization failed";
        return false;
    }

    // 保存波长和时间轴数据 (所有版本共有)
    for (int lambda = 0; lambda < static_cast<int>(waveChannels); ++lambda) {
        if (lambda >= 0 && lambda < waveAxisArray.size()) {
            out << waveAxisArray[lambda];
            if(out.status() != QDataStream::Ok) {
                qCritical() << "Wave axis serialization failed at index" << lambda;
                return false;
            }
        } else {
            qCritical() << "Index out of bounds in waveAxisArray: " << lambda;
            return false;
        }
    }

    for (int t = 0; t < static_cast<int>(timeChannels); ++t) {
        if (t >= 0 && t < timeAxisArray.size()) {
            out << timeAxisArray[t];
            if(out.status() != QDataStream::Ok) {
                qCritical() << "Time axis serialization failed at index" << t;
                return false;
            }
        } else {
            qCritical() << "Index out of bounds in timeAxisArray: " << t;
            return false;
        }
    }

    // 注意：在const方法中不能修改成员变量或调用非const方法
    // dataArraySize += dataArray.size();  // 不能在const方法中修改
    // saveDataArrayToFile();  // 不能在const方法中调用非const方法

    // 计算当前数据大小用于序列化
    qint64 currentDataArraySize = dataArraySize + dataArray.size();

    // 保存数据数组 (所有版本共有)
    for (int lambda = 0; lambda < static_cast<int>(waveChannels); ++lambda) {
        if (lambda >= 0 && lambda < dataArrayTotal.size()) {
            for (int t = 0; t < static_cast<int>(timeChannels); ++t) {
                if (t >= 0 && t < dataArrayTotal[lambda].size()) {
                    out << dataArrayTotal[lambda][t];
                    if(out.status() != QDataStream::Ok) {
                        qCritical() << "Total data serialization failed at (" << lambda << "," << t << ")";
                        return false;
                    }
                } else {
                    qCritical() << "Index out of bounds in dataArrayTotal[lambda]: " << t;
                    return false;
                }
            }
        } else {
            qCritical() << "Index out of bounds in dataArrayTotal: " << lambda;
            return false;
        }
    }

    // 版本特定的数据保存
    if (version >= FileVersion::VERSION_1) {
        // 版本1及以上特有的数据
        out << currentDataArraySize;
        if(out.status() != QDataStream::Ok) {
            qCritical() << "Data array size serialization failed";
            return false;
        }
    }

    // 未来版本可以在这里添加更多的版本特定数据保存
    // if (version >= FileVersion::VERSION_2) {
    //     // 版本2及以上特有的数据
    //     out << newField1 << newField2;
    // }

    return true;
}

bool MeasureDataHandler::deserializeFromStream(QDataStream &in, qint32 version)
{
    if (!in.device() || !in.device()->isReadable()) {
        qCritical() << "Open project failed: device not readable";
        return false;
    }

    // 读取基本信息 (所有版本共有)
    in >> errorFlag >> ident >> softwareVersion >> hardwareVersion >> fileTime >> fileChangeTime >> comment >> measurementMode >> displayLinLog >> displayWaveAxisLower >> displayWaveAxisUpper >> displayTimeAxisLower >> displayTimeAxisUpper >> displayCountAxisLower >> displayCountAxisUpper;
    if(in.status() != QDataStream::Ok) {
        qCritical() << "File header deserialization failed";
        return false;
    }

    // 读取测量参数 (所有版本共有)
    in >> monoValid >> monoGrating >> monoGroove >> monoBlaze >> monoWave >> monoPort >> monoShutter >> monoFilter >> tdcResolution >> waveChannels >> timeChannels;
    if(in.status() != QDataStream::Ok) {
        qCritical() << "Measurement parameters deserialization failed";
        return false;
    }

    // 增加边界检查
    if (waveChannels <= 0 || timeChannels <= 0) {
        qCritical() << "Invalid wave or time channels value";
        return false;
    }

    // 读取 TDC 分辨率数组 (所有版本共有)
    tdcResolutionArray.resize(waveChannels);
    for (int i = 0; i < static_cast<int>(waveChannels); ++i) {
        in >> tdcResolutionArray[i];
        if(in.status() != QDataStream::Ok) {
            qCritical() << "TDC resolution deserialization failed at index" << i;
            return false;
        }
    }

    // 读取停止条件 (所有版本共有)
    in >> stopCondition >> maxMeasurementTiming >> maxNumPhotons >> startTime >> endTime;
    if(in.status() != QDataStream::Ok) {
        qCritical() << "Stop condition deserialization failed";
        return false;
    }

    // 读取波长和时间轴数据 (所有版本共有)
    waveAxisArray.resize(waveChannels);
    timeAxisArray.resize(timeChannels);

    for (int lambda = 0; lambda < static_cast<int>(waveChannels); ++lambda) {
        in >> waveAxisArray[lambda];
        if(in.status() != QDataStream::Ok) {
            qCritical() << "Wave axis deserialization failed at index" << lambda;
            return false;
        }
    }
    for (int t = 0; t < static_cast<int>(timeChannels); ++t) {
        in >> timeAxisArray[t];
        if(in.status() != QDataStream::Ok) {
            qCritical() << "Time axis deserialization failed at index" << t;
            return false;
        }
    }

    // 读取数据数组 (所有版本共有)
    dataArrayTotal.resize(waveChannels);
    for (int lambda = 0; lambda < static_cast<int>(waveChannels); ++lambda) {
        dataArrayTotal[lambda].resize(timeChannels);
        for (int t = 0; t < static_cast<int>(timeChannels); ++t) {
            in >> dataArrayTotal[lambda][t];
            if(in.status() != QDataStream::Ok) {
                qCritical() << "Total data deserialization failed at (" << lambda << "," << t << ")";
                return false;
            }
        }
    }

    // 版本特定的数据读取
    if (version >= FileVersion::VERSION_1) {
        // 版本1及以上特有的数据
        in >> dataArraySize;
        if(in.status() != QDataStream::Ok) {
            qWarning() << "Data array size deserialization failed, using default value";
            dataArraySize = 0;  // 使用默认值
        }
    } else {
        // 旧版本没有 dataArraySize，设置为默认值
        dataArraySize = 0;
        qDebug() << "Legacy file format detected, dataArraySize set to 0";
    }

    // 未来版本可以在这里添加更多的版本特定数据读取
    // if (version >= FileVersion::VERSION_2) {
    //     // 版本2及以上特有的数据
    //     in >> newField1 >> newField2;
    // }

    return true;
}

QByteArray MeasureDataHandler::serialize() const {
    QBuffer buffer;
    buffer.open(QIODevice::ReadWrite);
    QDataStream out(&buffer);

    // 序列化当前版本的数据
    if (!serializeToStream(out, FileVersion::VERSION_CURRENT)) {
        qWarning() << "Failed to serialize MeasureDataHandler data";
        return QByteArray();
    }

    return buffer.data();
}

bool MeasureDataHandler::deserialize(const QByteArray& data) {
    if (data.isEmpty()) {
        qWarning() << "Cannot deserialize empty data";
        return false;
    }

    QBuffer buffer;
    buffer.setData(data);
    buffer.open(QIODevice::ReadOnly);
    QDataStream in(&buffer);

    // 反序列化数据（使用当前版本）
    return deserializeFromStream(in, FileVersion::VERSION_CURRENT);
}

// DEPRECATED: File I/O responsibilities have been moved to ProjectFileManager
// This method is kept for backward compatibility only
// Use serialize()/deserialize() methods instead
bool MeasureDataHandler::openProject(const QString &filePath) {
    qWarning() << "MeasureDataHandler::openProject(QString) is deprecated. Use ProjectFileManager for file operations.";

    // For backward compatibility, attempt basic file loading
    this->filePath = filePath;

    // Return false to indicate that file I/O should be handled by ProjectFileManager
    return false;
}

void MeasureDataHandler::createAndAppendDataFrame(quint16 frameNumber, quint32 pulseCount, const QVector<QVector<int>> &data)
{
    // 检查数据大小是否符合预期
    if (data.size() != waveChannels || data[0].size() != timeChannels) {
        qCritical() << "Invalid data size for DataFrame";
        return;
    }

    DataFrame newFrame;
    newFrame.frameNumber = frameNumber;
    newFrame.pulseCount = pulseCount;
    newFrame.data = data;
    dataArray.append(newFrame);
    dataArraySize += 1;

    // 更新上次保存时间
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

    // 检查系统内存使用情况
    qint64 availableMemory = getAvailableSystemMemory();

    // 如果可用内存低于1GB或dataArray超过frameInFile，保存数据
    if (availableMemory < 1024) {
        qDebug() << "Low memory detected (" << availableMemory << "MB). Forcing data save.";
        // 使用安全的保存方法
        saveDataArrayToFile();
        dataArray.clear();
        m_lastSaveTime = currentTime;
    }
    // 如果dataArray超过frameInFile，保存到filePath+.n文件并清空dataArray
    else if (dataArray.size() >= frameInFile) {
        // 使用安全的保存方法
        saveDataArrayToFile();
        dataArray.clear();
        m_lastSaveTime = currentTime;
    }
}

void MeasureDataHandler::saveDataArrayToFile()
{
    if (dataArray.isEmpty()) {
        return;
    }
    // 复制dataArray和其他必要的数据
    QList<DataFrame> dataArrayCopy = dataArray;
    QString filePathCopy = filePath;
    unsigned int waveChannelsCopy = waveChannels;
    unsigned int timeChannelsCopy = timeChannels;
    int fileCounterValue = fileCounter++;

    // 异步调用保存方法，但不要捕获 this 指针
    QtConcurrent::run([filePathCopy, dataArrayCopy, waveChannelsCopy, timeChannelsCopy, fileCounterValue]() {
        // 创建一个独立的保存函数，不依赖于类成员变量
        QString dataFilePath = QString("%1.%2").arg(filePathCopy).arg(fileCounterValue);
        QFile file(dataFilePath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
            QBuffer buffer;
            buffer.open(QIODevice::ReadWrite);
            QDataStream out(&buffer);

            out << static_cast<qint32>(dataArrayCopy.size()); // 写入dataArrayCopy的大小
            for (const auto& frame : dataArrayCopy) {
                out << frame.frameNumber << frame.pulseCount;
                for (int lambda = 0; lambda < waveChannelsCopy; ++lambda) {
                    for (int t = 0; t < timeChannelsCopy; ++t) {
                        out << frame.data[lambda][t];
                    }
                }
            }

            QByteArray compressed = qCompress(buffer.data());

            QDataStream fileOut(&file);
            fileOut.setVersion(QDataStream::Qt_DefaultCompiledVersion);

            // 写入文件头
            fileOut << qint32(0x4D435A50); // 魔数标识压缩格式

            // 写入版本号 (确保是一个合理的值)
            fileOut << qint32(MeasureDataHandler::FileVersion::VERSION_CURRENT); // 版本号

            // 写入压缩数据长度
            fileOut << qint32(compressed.size()); // 压缩数据长度

            // 写入压缩数据
            qint64 bytesWritten = fileOut.writeRawData(compressed.constData(), compressed.size());
            if (bytesWritten != compressed.size()) {
                qCritical() << "Failed to write all compressed data. Expected:" << compressed.size() << "Actual:" << bytesWritten;
                file.close();
                return;
            }

            qDebug() << "Saved data file with version:" << MeasureDataHandler::FileVersion::VERSION_CURRENT
                     << "compressed size:" << compressed.size()
                     << "total file size:" << file.size();

            file.flush();
            file.close();
        } else {
            qCritical() << "Failed to open file for writing:" << dataFilePath;
        }
    });
}

void MeasureDataHandler::saveDataArrayToFileAsync(const QList<DataFrame> &dataArrayCopy)
{
    QString dataFilePath = QString("%1.%2").arg(filePath).arg(fileCounter++);
    QFile file(dataFilePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        QBuffer buffer;
        buffer.open(QIODevice::ReadWrite);
        QDataStream out(&buffer);

        out << static_cast<qint32>(dataArrayCopy.size()); // 写入dataArrayCopy的大小
        for (const auto& frame : dataArrayCopy) {
            out << frame.frameNumber << frame.pulseCount;
            for (int lambda = 0; lambda < waveChannels; ++lambda) {
                for (int t = 0; t < timeChannels; ++t) {
                    out << frame.data[lambda][t];
                }
            }
        }

        QByteArray compressed = qCompress(buffer.data());

        QDataStream fileOut(&file);
        fileOut.setVersion(QDataStream::Qt_DefaultCompiledVersion);

        // 写入文件头
        fileOut << qint32(0x4D435A50); // 魔数标识压缩格式

        // 写入版本号 (确保是一个合理的值)
        fileOut << qint32(FileVersion::VERSION_CURRENT); // 版本号

        // 写入压缩数据长度
        fileOut << qint32(compressed.size()); // 压缩数据长度

        // 写入压缩数据
        qint64 bytesWritten = fileOut.writeRawData(compressed.constData(), compressed.size());
        if (bytesWritten != compressed.size()) {
            qCritical() << "Failed to write all compressed data. Expected:" << compressed.size() << "Actual:" << bytesWritten;
            file.close();
            return;
        }

        qDebug() << "Saved data file with version:" << FileVersion::VERSION_CURRENT
                 << "compressed size:" << compressed.size()
                 << "total file size:" << file.size();

        file.flush();
        file.close();
    } else {
        qCritical() << "Failed to open file for writing:" << dataFilePath;
    }
}

void MeasureDataHandler::start(const QString &path)
{
    // 确保文件路径使用.sflp扩展名
    QString sflpPath = path;
    if (!sflpPath.endsWith(".sflp", Qt::CaseInsensitive)) {
        // 如果路径以.sfd结尾，替换为.sflp
        if (sflpPath.endsWith(".sfd", Qt::CaseInsensitive)) {
            sflpPath = sflpPath.left(sflpPath.length() - 4) + ".sflp";
        } else {
            // 如果没有扩展名，添加.sflp
            sflpPath += ".sflp";
        }
        qDebug() << "Updated file path to use SFLP format:" << sflpPath;
    }

    filePath = sflpPath;

    // 重置dataArray
    dataArray.clear();
    // 重置dataArraySize
    dataArraySize = 0;
    // 重置startTime
    startTime = QDateTime::currentMSecsSinceEpoch();
    // 重置endTime
    endTime = 0;
    // 重置saving
    saving = true;
    // 重置lastSaveTime
    m_lastSaveTime = startTime;

    // 重新计算最佳FrameInFile值
    calculateOptimalFrameInFile();
}

void MeasureDataHandler::stop()
{
    // 重置endTime
    endTime = QDateTime::currentMSecsSinceEpoch();
    // 重置saving
    saving = false;
}

// 实现新增方法：计算最佳FrameInFile值
void MeasureDataHandler::calculateOptimalFrameInFile()
{
    // 获取系统可用内存（MB）
    qint64 availableMemory = getAvailableSystemMemory();

    // 获取磁盘可用空间
    QStorageInfo storageInfo(QDir::currentPath());
    qint64 availableDiskSpace = storageInfo.bytesAvailable() / (1024 * 1024); // 转换为MB

    // 估算单个数据帧大小（MB）
    double frameSizeMB = static_cast<double>(m_estimatedFrameSize) / (1024 * 1024);

    // 计算最佳值：使用不超过系统可用内存的10%
    int optimalFrames = static_cast<int>(availableMemory * 0.1 / frameSizeMB);

    // 确保在合理范围内
    frameInFile = qBound(100, optimalFrames, 5000);

    qDebug() << "Calculated optimal FrameInFile:" << frameInFile
             << "(Available Memory:" << availableMemory << "MB, Frame Size:" << frameSizeMB << "MB)";
}

// 实现新增方法：检查并保存数据（基于时间的保存策略）
void MeasureDataHandler::checkAndSaveData()
{
    if (!saving || dataArray.isEmpty()) {
        return;
    }

    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    // 如果距离上次保存已经超过30秒，则保存数据
    if (currentTime - m_lastSaveTime > 30000) {
        qDebug() << "Time-based save triggered. Saving" << dataArray.size() << "frames.";
        // 使用安全的保存方法
        saveDataArrayToFile();
        dataArray.clear();
        m_lastSaveTime = currentTime;
    }

    // 检查系统内存使用情况
    qint64 availableMemory = getAvailableSystemMemory();
    // 如果可用内存低于1GB，强制保存
    if (availableMemory < 1024) {
        qDebug() << "Low memory detected (" << availableMemory << "MB). Forcing data save.";
        if (!dataArray.isEmpty()) {
            // 使用安全的保存方法
            saveDataArrayToFile();
            dataArray.clear();
            m_lastSaveTime = currentTime;
        }
    }
}

// 实现新增方法：获取系统可用内存（MB）
qint64 MeasureDataHandler::getAvailableSystemMemory()
{
#ifdef Q_OS_WIN
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);
    return memInfo.ullAvailPhys / (1024 * 1024); // 转换为MB
#else
    // 在非Windows系统上返回一个默认值
    return 4096; // 默认4GB
#endif
}
