#include "TabSynchronizer.h"
#include "ProcessTab.h"
#include "Analysis.h"
#include "AcquireTab.h"
#include "SharedDataManager.h"
#include "Constants.h"
#include "ProjectFileManager.h"
#include <QMetaObject>
#include <QMetaMethod>
#include <QMessageBox>

TabSynchronizer* TabSynchronizer::instance = nullptr;

TabSynchronizer* TabSynchronizer::getInstance() {
    if (!instance) {
        instance = new TabSynchronizer();
    }
    return instance;
}

TabSynchronizer::TabSynchronizer(QObject* parent) : QObject(parent) {
    // 初始化
}

TabSynchronizer::~TabSynchronizer() {
    // Clean up
}

// Plot synchronization methods
void TabSynchronizer::registerPlot(TabType tab, PlotType plotType, QCustomPlot* plot) {
    if (plot) {
        m_plots[tab][plotType] = plot;
    }
}

void TabSynchronizer::unregisterPlot(QCustomPlot* plot) {
    // Find and remove the plot from our maps
    for (auto tabIt = m_plots.begin(); tabIt != m_plots.end(); ++tabIt) {
        for (auto plotIt = tabIt.value().begin(); plotIt != tabIt.value().end(); ++plotIt) {
            if (plotIt.value() == plot) {
                tabIt.value().remove(plotIt.key());
                return;
            }
        }
    }
}
// OpenProjectWidget synchronization methods
void TabSynchronizer::registerOpenProjectWidget(OpenProjectWidget* widget, TabType tab) {
    if (widget) {
        m_openProjectWidgets[tab] = widget;
    }
}

void TabSynchronizer::unregisterOpenProjectWidget(OpenProjectWidget* widget) {
    // Find and remove the widget from our map
    for (auto it = m_openProjectWidgets.begin(); it != m_openProjectWidgets.end(); ++it) {
        if (it.value() == widget) {
            // Disconnect signals
            disconnect(widget, nullptr, this, nullptr);
            m_openProjectWidgets.remove(it.key());
            return;
        }
    }
}

// 处理标签页切换事件
void TabSynchronizer::onTabChanged(int index) {
    // 获取当前活动的标签页类型
    QTabWidget* tabWidget = qobject_cast<QTabWidget*>(sender());
    if (!tabWidget) {
        qWarning() << "TabSynchronizer::onTabChanged - sender is not a QTabWidget";
        return;
    }

    // 获取当前标签页
    QWidget* currentWidget = tabWidget->widget(index);
    if (!currentWidget) {
        qWarning() << "TabSynchronizer::onTabChanged - current widget is null";
        return;
    }

    // 确定当前标签页类型
    TabType currentTabType = TabType::Acquire;
    if (qobject_cast<AnalysisTab*>(currentWidget)) {
        currentTabType = TabType::Analysis;
        qDebug() << "Switched to AnalysisTab - checking for data updates";

        // 当切换到Analysis标签页时，检查数据是否已加载完成
        ProjectFileManager* fileManager = ProjectFileManager::getInstance();
        if (fileManager->isProjectLoading()) {
            // 显示加载提示
            QMessageBox::information(nullptr, "Information", "Project data is still loading. Please wait...");
            return;
        }

        // 当切换到Analysis标签页时，执行以下同步操作：

        // 1. 同步文件选择
        syncFileSelection(TabType::Process, TabType::Analysis);
    }

    qDebug() << "Tab changed to index:" << index << "TabType:" << static_cast<int>(currentTabType);
}



// 同步工具栏状态
void TabSynchronizer::syncToolbarStates(TabType sourceTab, TabType targetTab) {
    qDebug() << "Synchronizing toolbar states from tab" << static_cast<int>(sourceTab)
             << "to tab" << static_cast<int>(targetTab);

    // 检查源标签页和目标标签页是否存在
    if (!m_plots.contains(sourceTab) || !m_plots.contains(targetTab)) {
        qWarning() << "Source or target tab not found in registered plots";
        return;
    }

    // 获取源标签页和目标标签页的父组件
    QWidget* sourceWidget = nullptr;
    QWidget* targetWidget = nullptr;

    // 找到源标签页的父组件
    for (auto plotIt = m_plots[sourceTab].begin(); plotIt != m_plots[sourceTab].end(); ++plotIt) {
        QCustomPlot* plot = plotIt.value();
        if (plot) {
            QWidget* parentWidget = plot->parentWidget();
            while (parentWidget && !dynamic_cast<AcquireTab*>(parentWidget) &&
                   !dynamic_cast<ProcessTab*>(parentWidget) && !dynamic_cast<AnalysisTab*>(parentWidget)) {
                parentWidget = parentWidget->parentWidget();
            }

            if (parentWidget) {
                sourceWidget = parentWidget;
                break;
            }
        }
    }

    // 找到目标标签页的父组件
    for (auto plotIt = m_plots[targetTab].begin(); plotIt != m_plots[targetTab].end(); ++plotIt) {
        QCustomPlot* plot = plotIt.value();
        if (plot) {
            QWidget* parentWidget = plot->parentWidget();
            while (parentWidget && !dynamic_cast<AcquireTab*>(parentWidget) &&
                   !dynamic_cast<ProcessTab*>(parentWidget) && !dynamic_cast<AnalysisTab*>(parentWidget)) {
                parentWidget = parentWidget->parentWidget();
            }

            if (parentWidget) {
                targetWidget = parentWidget;
                break;
            }
        }
    }

    if (!sourceWidget || !targetWidget) {
        qWarning() << "Source or target widget not found";
        return;
    }

    // 获取源标签页和目标标签页的BaseTab对象
    BaseTab* sourceTab_ptr = dynamic_cast<BaseTab*>(sourceWidget);
    BaseTab* targetTab_ptr = dynamic_cast<BaseTab*>(targetWidget);

    if (!sourceTab_ptr || !targetTab_ptr) {
        qWarning() << "Source or target tab is not a BaseTab";
        return;
    }

    // 同步Plot1工具栏状态
    if (sourceTab_ptr->m_pPlot1Toolbar && targetTab_ptr->m_pPlot1Toolbar) {
        // 获取源工具栏的状态
        // 不再同步十字线状态
        bool xAxisLogState = sourceTab_ptr->m_pPlot1Toolbar->getXAxisLogState();
        bool yAxisLogState = sourceTab_ptr->m_pPlot1Toolbar->getYAxisLogState();
        bool zAxisLogState = sourceTab_ptr->m_pPlot1Toolbar->getZAxisLogState();
        bool zoomInState = sourceTab_ptr->m_pPlot1Toolbar->getZoomInState();
        bool zoomOutState = sourceTab_ptr->m_pPlot1Toolbar->getZoomOutState();

        // 直接触发对应的信号，这会自动处理所有相关的状态和操作
        // 不再同步十字线状态

        if (xAxisLogState != targetTab_ptr->m_pPlot1Toolbar->getXAxisLogState()) {
            targetTab_ptr->m_pPlot1Toolbar->setXAxisLogState(xAxisLogState);
        }

        if (yAxisLogState != targetTab_ptr->m_pPlot1Toolbar->getYAxisLogState()) {
            targetTab_ptr->m_pPlot1Toolbar->setYAxisLogState(yAxisLogState);
        }

        if (zAxisLogState != targetTab_ptr->m_pPlot1Toolbar->getZAxisLogState()) {
            targetTab_ptr->m_pPlot1Toolbar->setZAxisLogState(zAxisLogState);
        }

        if (zoomInState != targetTab_ptr->m_pPlot1Toolbar->getZoomInState()) {
            targetTab_ptr->m_pPlot1Toolbar->setZoomInState(zoomInState);
        }

        if (zoomOutState != targetTab_ptr->m_pPlot1Toolbar->getZoomOutState()) {
            targetTab_ptr->m_pPlot1Toolbar->setZoomOutState(zoomOutState);
        }
    }

    // 同步Plot2工具栏状态
    if (sourceTab_ptr->m_pPlot2Toolbar && targetTab_ptr->m_pPlot2Toolbar) {
        // 获取源工具栏的状态
        bool xAxisLogState = sourceTab_ptr->m_pPlot2Toolbar->getXAxisLogState();
        bool yAxisLogState = sourceTab_ptr->m_pPlot2Toolbar->getYAxisLogState();

        // 直接触发对应的信号，这会自动处理所有相关的状态和操作
        if (xAxisLogState != targetTab_ptr->m_pPlot2Toolbar->getXAxisLogState()) {
            targetTab_ptr->m_pPlot2Toolbar->setXAxisLogState(xAxisLogState);
        }

        if (yAxisLogState != targetTab_ptr->m_pPlot2Toolbar->getYAxisLogState()) {
            targetTab_ptr->m_pPlot2Toolbar->setYAxisLogState(yAxisLogState);
        }
    }

    // 同步Plot3工具栏状态
    if (sourceTab_ptr->m_pPlot3Toolbar && targetTab_ptr->m_pPlot3Toolbar) {
        // 获取源工具栏的状态
        bool yAxisLogState = sourceTab_ptr->m_pPlot3Toolbar->getYAxisLogState();

        // 直接触发对应的信号，这会自动处理所有相关的状态和操作
        if (yAxisLogState != targetTab_ptr->m_pPlot3Toolbar->getYAxisLogState()) {
            targetTab_ptr->m_pPlot3Toolbar->setYAxisLogState(yAxisLogState);
        }
    }

    qDebug() << "Toolbar states synchronized successfully";
}

// 同步文件选择
void TabSynchronizer::syncFileSelection(TabType sourceTab, TabType targetTab) {
    qDebug() << "Synchronizing file selection from tab" << static_cast<int>(sourceTab)
             << "to tab" << static_cast<int>(targetTab);

    // 检查源标签页和目标标签页是否存在
    if (!m_openProjectWidgets.contains(sourceTab) || !m_openProjectWidgets.contains(targetTab)) {
        qWarning() << "Source or target tab not found in registered OpenProjectWidgets";
        return;
    }

    OpenProjectWidget* sourceWidget = m_openProjectWidgets[sourceTab];
    OpenProjectWidget* targetWidget = m_openProjectWidgets[targetTab];

    if (!sourceWidget || !targetWidget) {
        qWarning() << "Source or target OpenProjectWidget is null";
        return;
    }

    // 获取源标签页当前选中的文件
    QString selectedFileName = sourceWidget->getSelectedFileName();

    if (selectedFileName.isEmpty()) {
        qDebug() << "No file selected in source tab";
        return;
    }

    qDebug() << "Selected file in source tab:" << selectedFileName;

    // 获取目标标签页当前选中的文件
    QString targetSelectedFileName = targetWidget->getSelectedFileName();

    // 检查目标标签页当前选择是否与源标签页一致，只有不一致才触发选择事件
    if (selectedFileName != targetSelectedFileName) {
        qDebug() << "Target tab has different file selected:" << targetSelectedFileName
                 << ", updating to match source tab";
        // 在目标标签页中选中相同的文件，并触发onFileSelected事件
        targetWidget->selectFileByName(selectedFileName, true);
        qDebug() << "File selection synchronized successfully (with triggering onFileSelected)";
    } else {
        qDebug() << "Target tab already has the same file selected, no need to update";
    }
}