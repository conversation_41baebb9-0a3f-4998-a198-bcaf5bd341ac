#ifndef SFLPFILEOPERATIONS_H
#define SFLPFILEOPERATIONS_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QMap>
#include <QMutex>
#include "UnifiedFileStructures.h"
#include "SflpFileManager.h"

/**
 * @brief SFLP文件操作辅助类
 * 
 * 负责SFLP文件的具体操作逻辑，包括数据段的读写、压缩、验证等功能。
 * 这个类将SFLP文件的复杂操作从ProjectFileManager中分离出来，
 * 提供更清晰的职责分工和更好的代码组织。
 */
class SflpFileOperations : public QObject
{
    Q_OBJECT

public:
    explicit SflpFileOperations(QObject* parent = nullptr);
    ~SflpFileOperations();

    // 数据段操作
    bool writeOperationData(const QString& sflpFileName,
                           const QString& operationName,
                           const PlotDataCollection& plotData);
    
    bool writeAnalysisData(const QString& sflpFileName,
                          const QString& analysisName,
                          const AnalysisResults& results);
    
    PlotDataCollection readOperationData(const QString& sflpFileName,
                                        const QString& operationName);
    
    AnalysisResults readAnalysisData(const QString& sflpFileName,
                                    const QString& analysisName);

    // 操作计数器管理
    bool writeOperationCounters(const QString& sflpFileName,
                               const OperationCounters& counters);

    OperationCounters readOperationCounters(const QString& sflpFileName);

    // 项目元数据管理
    bool writeProjectMetadata(const QString& sflpFileName,
                             const QByteArray& metadataBytes);

    QByteArray readProjectMetadata(const QString& sflpFileName);

    // 文件验证和维护
    bool validateSflpFile(const QString& sflpFileName);
    bool repairSflpFile(const QString& sflpFileName);
    bool compactSflpFile(const QString& sflpFileName);
    
    // 数据段管理
    QStringList getDataSegmentNames(const QString& sflpFileName);
    bool removeDataSegment(const QString& sflpFileName, const QString& segmentName);
    qint64 getDataSegmentSize(const QString& sflpFileName, const QString& segmentName);
    
    // 批量操作
    bool writeBatchData(const QString& sflpFileName,
                       const QMap<QString, QByteArray>& dataMap);
    QMap<QString, QByteArray> readBatchData(const QString& sflpFileName,
                                           const QStringList& segmentNames);

    // 压缩工具
    static QByteArray compressData(const QByteArray& rawData);
    static QByteArray decompressData(const QByteArray& compressedData);

    // 错误处理
    QString getLastError() const;
    bool hasError() const;
    void clearError();

signals:
    void operationDataWritten(const QString& sflpFileName, const QString& operationName);
    void analysisDataWritten(const QString& sflpFileName, const QString& analysisName);
    void dataSegmentRemoved(const QString& sflpFileName, const QString& segmentName);
    void fileValidationCompleted(const QString& sflpFileName, bool isValid);
    void errorOccurred(const QString& sflpFileName, const QString& error);

private:
    // SFLP文件管理器缓存
    QMap<QString, SflpFileManager*> m_managerCache;
    mutable QMutex m_cacheMutex;
    
    // 错误处理
    QString m_lastError;
    mutable QMutex m_errorMutex;

    // 内部方法
    SflpFileManager* getManager(const QString& sflpFileName);
    void releaseManager(const QString& sflpFileName);
    void cleanupManagers();
    
    QString generateOperationSegmentName(const QString& operationName) const;
    QString generateAnalysisSegmentName(const QString& analysisName) const;
    QString generateCountersSegmentName() const;
    
    bool writeDataSegment(const QString& sflpFileName,
                         const QString& segmentName,
                         const QByteArray& data);
    QByteArray readDataSegment(const QString& sflpFileName,
                              const QString& segmentName);
    
    void setError(const QString& error);
    void logOperation(const QString& operation, const QString& fileName, bool success);
    
    // 数据验证
    bool validateOperationData(const PlotDataCollection& plotData) const;
    bool validateAnalysisData(const AnalysisResults& results) const;
    bool validateSegmentName(const QString& segmentName) const;
    
    // 禁用拷贝构造和赋值
    SflpFileOperations(const SflpFileOperations&) = delete;
    SflpFileOperations& operator=(const SflpFileOperations&) = delete;
};

#endif // SFLPFILEOPERATIONS_H
