# SpecFLIM文件管理架构重构设计文档 - SFD到SFLP迁移

## 1. 概述

### 1.1 设计目标
本文档定义了SpecFLIM应用程序从SFD格式向SFLP格式迁移的完整架构设计，实现统一的文件管理系统，支持从Acquire测量数据到Process操作数据再到Analysis分析数据的完整生命周期管理。

### 1.2 核心设计原则
- **格式统一**: 全面迁移到SFLP格式，停止使用SFD格式保存
- **向后兼容**: 保持对现有SFD文件的读取兼容性
- **数据完整性**: 确保MeasureDataHandler现有功能100%保持
- **虚拟层级**: 支持操作数据和分析数据作为虚拟子项的层级显示
- **Qt最佳实践**: 严格遵循Qt类型和内存管理模式
- **生产就绪**: 提供完整的错误处理和参数验证

### 1.3 迁移策略
- **测量数据**: 从SFD格式迁移到SFLP格式，包括帧数据统一存储
- **帧数据整合**: 将分散的sfd.x文件整合到SFLP数据段中
- **格式转换**: 实现SFD到SFLP的完整转换功能
- **兼容性保证**: 保持对现有SFD文件的读取支持，但新数据仅保存为SFLP格式

## 2. 核心架构组件

### 2.1 FileNameManager类设计

```cpp
class FileNameManager {
public:
    static FileNameManager* getInstance();

    // 项目前缀管理（简化版）
    QString getProjectPrefix() const;  // 从AppConfig读取，默认"SFL"

    // 工作区文件命名
    QString generateWorkspaceFileName() const;
    QString generateWorkspaceFileName(const QDate& date) const;

    // 工程文件命名
    QString generateProjectFileName(int sequence) const;
    QString generateProjectFileName(const QDate& date, int sequence) const;

    // 操作序列命名
    QString generateOperationName(const QString& baseFileName,
                                 const OperationSequence& sequence) const;

    // 拟合数据命名（修改为新格式）
    QString generateDecayAnalysisName(const QString& baseFileName,
                                     int sequence) const;  // DecAna_{序号}
    QString generateSpectralAnalysisName(const QString& baseFileName,
                                        int sequence) const;  // SpeAna_{序号}

    // Split文件命名
    QString generateSplitFileName(const QString& originalFileName,
                                 int splitSequence) const;

    // 显示名称生成
    QString generateDisplayName(const QString& fileName,
                               FileType fileType) const;

    // 序列号管理（sflp文件范围）
    int getNextProjectSequence() const;
    int getNextOperationSequence(const QString& sflpFileName,
                                OperationType operationType) const;
    int getNextAnalysisSequence(const QString& sflpFileName,
                               AnalysisType analysisType) const;

private:
    AppConfig* m_appConfig;

    // 内部辅助方法
    QString formatDateString(const QDate& date) const;
    bool isValidFileName(const QString& fileName) const;
    QString getDefaultProjectPrefix() const { return "SFL"; }
};

// 支持数据结构
enum class OperationType {
    Alignment,  // al
    Crop,       // cr
    AddCurve    // ad
};

enum class AnalysisType {
    DecayAnalysis,    // DecAna
    SpectralAnalysis  // SpeAna
};

enum class FileType {
    Workspace,      // .sflw
    Project,        // .sflp
    Operation,      // 虚拟子项
    DecayAnalysis,  // DecAna虚拟子项
    SpectralAnalysis, // SpeAna虚拟子项
    Split          // _split.sflp
};

struct OperationCounters {
    int alignmentCounter = 0;
    int cropCounter = 0;
    int addCurveCounter = 0;
    int decayAnalysisCounter = 0;
    int spectralAnalysisCounter = 0;

    int getCounter(OperationType type) const;
    int getCounter(AnalysisType type) const;
    void incrementCounter(OperationType type);
    void incrementCounter(AnalysisType type);
    void setCounter(OperationType type, int value);
    void setCounter(AnalysisType type, int value);
};

struct OperationSequence {
    QList<QPair<OperationType, int>> operations;

    QString toString() const;
    static OperationSequence fromString(const QString& sequence);
    void appendOperation(OperationType type, int sequence);
    bool isEmpty() const;
};
```

### 2.2 精简的ProjectFileManager增强设计

```cpp
// 直接在现有ProjectFileManager类中添加统一文件管理接口
class ProjectFileManager : public QObject {
    Q_OBJECT

public:
    static ProjectFileManager* getInstance();

    // 现有接口保持不变...

    // 新增：统一文件操作接口
    bool saveOperationData(const QString& sflpFileName,
                          const OperationSequence& sequence,
                          const PlotDataCollection& plotData);

    bool saveAnalysisData(const QString& sflpFileName,
                         const QString& baseOperationName,
                         AnalysisType analysisType,
                         const AnalysisResults& results);

    PlotDataCollection loadOperationData(const QString& operationName);
    AnalysisResults loadAnalysisData(const QString& analysisName);

    // 新增：文件关系管理
    QStringList getChildFiles(const QString& parentFile) const;
    QString getParentFile(const QString& childFile) const;
    FileType getFileType(const QString& fileName) const;

    // 新增：操作计数器管理（sflp文件范围）
    OperationCounters getOperationCounters(const QString& sflpFileName) const;
    void updateOperationCounter(const QString& sflpFileName,
                               OperationType type, int newValue);
    void updateAnalysisCounter(const QString& sflpFileName,
                              AnalysisType type, int newValue);

    // 新增：SFLP格式支持
    bool saveToSflpFormat(const QString& sflpFileName,
                         const QByteArray& data,
                         const QString& dataSegmentName);
    QByteArray loadFromSflpFormat(const QString& sflpFileName,
                                 const QString& dataSegmentName);

    // 新增：压缩数据管理
    QByteArray compressData(const QByteArray& rawData) const;
    QByteArray decompressData(const QByteArray& compressedData) const;

signals:
    void operationDataSaved(const QString& operationName);
    void analysisDataSaved(const QString& analysisName);
    void fileRelationshipChanged();

private:
    // 现有成员变量保持不变...

    // 新增：文件关系缓存（简化版）
    QMap<QString, QStringList> m_childrenMap; // 父文件→子项列表
    QMap<QString, QString> m_parentMap;       // 子项→父文件

    // 新增：操作计数器缓存（按sflp文件管理）
    QMap<QString, OperationCounters> m_sflpCounters;

    // 新增：内部实现方法
    bool writeDataSegmentToSflp(const QString& sflpFileName,
                                const QString& segmentName,
                                const QByteArray& data);
    QByteArray readDataSegmentFromSflp(const QString& sflpFileName,
                                      const QString& segmentName);
    void initializeCountersFromSflp(const QString& sflpFileName);
    void updateFileRelationship(const QString& parent, const QString& child);
};
```

### 2.3 SFLP文件格式结构（优化版）

```cpp
// SFLP文件头结构（扩展保留字段）
struct SflpFileHeader {
    quint32 magicNumber;        // 0x534C4650 ("SFLP")
    quint32 version;            // 文件格式版本
    quint32 headerSize;         // 文件头大小
    quint32 indexOffset;        // 索引表偏移
    quint32 indexSize;          // 索引表大小
    quint32 dataSegmentCount;   // 数据段数量
    quint64 totalFileSize;      // 文件总大小
    quint32 compressionType;    // 压缩类型 (0=无压缩, 1=Qt压缩)
    quint32 reserved[16];       // 保留字段（扩展为16个）

    bool isValid() const;
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 数据段索引项
struct DataSegmentIndex {
    QString segmentName;        // 数据段名称
    quint64 offset;            // 数据段偏移
    quint64 compressedSize;    // 压缩后大小
    quint64 originalSize;      // 原始大小
    quint32 checksum;          // 数据校验和
    QDateTime timestamp;       // 创建时间

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 简化的SFLP文件管理器（集成到ProjectFileManager中）
class SflpFileManager {
public:
    explicit SflpFileManager(const QString& fileName);

    // 基本文件操作
    bool openFile(QIODevice::OpenMode mode);
    void closeFile();
    bool isOpen() const;

    // 数据段操作
    bool writeDataSegment(const QString& segmentName,
                         const QByteArray& data);
    QByteArray readDataSegment(const QString& segmentName);
    QStringList getDataSegmentNames() const;

    // 索引管理
    bool rebuildIndex();
    bool validateFile() const;

private:
    QString m_fileName;
    QFile* m_file;
    SflpFileHeader m_header;
    QMap<QString, DataSegmentIndex> m_indexMap;

    bool readHeader();
    bool writeHeader();
    bool readIndex();
    bool writeIndex();
    quint32 calculateChecksum(const QByteArray& data) const;
};
```

## 3. 数据结构定义

### 3.1 核心数据结构

```cpp
// 绘图数据集合
struct PlotDataCollection {
    FluorescenceMapData fluorescenceMap;
    QVector<DecayCurveData> decayCurves;      // 仅已添加的曲线
    QVector<SpectralCurveData> spectralCurves; // 仅已添加的曲线
    TotalCountsData totalCounts;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    bool isValid() const;
    qint64 calculateSize() const;
};

// 操作元数据
struct OperationMetadata {
    OperationType operationType;
    QDateTime timestamp;
    QString operationName;
    OperationSequence sequence;
    QMap<QString, QVariant> parameters;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 分析结果数据（统一DecAna和SpeAna）
struct AnalysisResults {
    AnalysisType analysisType;  // DecayAnalysis 或 SpectralAnalysis
    QMap<QString, double> parameters;
    QVector<QPointF> fittedCurve;
    QVector<QPointF> residualCurve;
    QString analysisReport;
    double chiSquared;
    double rSquared;
    QDateTime timestamp;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 分析参数
struct AnalysisParameters {
    QString modelType;
    QMap<QString, double> initialValues;
    QMap<QString, QPair<double, double>> limits; // min, max
    QMap<QString, bool> fixedParameters;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};
```

## 4. 操作流程设计

### 4.1 操作保存流程（精简版）

```cpp
// Process操作保存流程
bool ProjectFileManager::saveOperationData(const QString& sflpFileName,
                                          const OperationSequence& sequence,
                                          const PlotDataCollection& plotData) {
    try {
        // 1. 生成操作名称
        FileNameManager* nameManager = FileNameManager::getInstance();
        QString operationName = nameManager->generateOperationName(sflpFileName, sequence);

        // 2. 创建操作元数据
        OperationMetadata metadata;
        metadata.operationType = sequence.operations.last().first;
        metadata.timestamp = QDateTime::currentDateTime();
        metadata.operationName = operationName;
        metadata.sequence = sequence;

        // 3. 序列化和压缩数据
        QByteArray plotDataBytes = plotData.serialize();
        QByteArray metadataBytes = metadata.serialize();
        QByteArray compressedPlotData = compressData(plotDataBytes);
        QByteArray compressedMetadata = compressData(metadataBytes);

        // 4. 保存到SFLP文件
        bool success = saveToSflpFormat(sflpFileName, compressedPlotData,
                                       operationName + "_data");
        success &= saveToSflpFormat(sflpFileName, compressedMetadata,
                                   operationName + "_meta");

        if (success) {
            // 5. 更新文件关系和计数器
            updateFileRelationship(sflpFileName, operationName);
            updateOperationCounter(sflpFileName, metadata.operationType,
                                 sequence.operations.last().second);
            emit operationDataSaved(operationName);
        }

        return success;

    } catch (const std::exception& e) {
        qCritical() << "Operation save failed:" << e.what();
        return false;
    }
}

// Analysis分析保存流程
bool ProjectFileManager::saveAnalysisData(const QString& sflpFileName,
                                         const QString& baseOperationName,
                                         AnalysisType analysisType,
                                         const AnalysisResults& results) {
    try {
        // 1. 生成分析序列号和名称
        FileNameManager* nameManager = FileNameManager::getInstance();
        int sequence = getNextAnalysisSequence(sflpFileName, analysisType);

        QString analysisName;
        if (analysisType == AnalysisType::DecayAnalysis) {
            analysisName = nameManager->generateDecayAnalysisName(baseOperationName, sequence);
        } else {
            analysisName = nameManager->generateSpectralAnalysisName(baseOperationName, sequence);
        }

        // 2. 序列化和压缩数据
        QByteArray resultsBytes = results.serialize();
        QByteArray compressedResults = compressData(resultsBytes);

        // 3. 保存到SFLP文件
        bool success = saveToSflpFormat(sflpFileName, compressedResults,
                                       analysisName + "_results");

        if (success) {
            // 4. 更新文件关系和计数器
            updateFileRelationship(baseOperationName, analysisName);
            updateAnalysisCounter(sflpFileName, analysisType, sequence);
            emit analysisDataSaved(analysisName);
        }

        return success;

    } catch (const std::exception& e) {
        qCritical() << "Analysis save failed:" << e.what();
        return false;
    }
}
```

### 4.2 数据加载流程

```cpp
// 数据加载流程（集成到ProjectFileManager中）
PlotDataCollection ProjectFileManager::loadOperationData(const QString& operationName) {
    try {
        // 1. 获取父文件
        QString parentFile = getParentFile(operationName);
        if (parentFile.isEmpty()) {
            throw std::runtime_error("Parent file not found");
        }

        // 2. 从SFLP文件读取压缩数据
        QByteArray compressedData = loadFromSflpFormat(parentFile, operationName + "_data");

        // 3. 解压缩数据
        QByteArray rawData = decompressData(compressedData);

        // 4. 反序列化数据
        PlotDataCollection plotData;
        if (!plotData.deserialize(rawData)) {
            throw std::runtime_error("Failed to deserialize plot data");
        }

        return plotData;

    } catch (const std::exception& e) {
        qCritical() << "Failed to load operation data:" << e.what();
        return PlotDataCollection(); // 返回空数据
    }
}

AnalysisResults ProjectFileManager::loadAnalysisData(const QString& analysisName) {
    try {
        // 1. 获取基础操作名称和父文件
        QString baseOperation = extractBaseOperationName(analysisName);
        QString parentFile = getParentFile(baseOperation);

        // 2. 读取分析结果
        QByteArray compressedResults = loadFromSflpFormat(parentFile, analysisName + "_results");
        QByteArray rawResults = decompressData(compressedResults);

        // 3. 反序列化分析结果
        AnalysisResults results;
        if (!results.deserialize(rawResults)) {
            throw std::runtime_error("Failed to deserialize analysis results");
        }

        return results;

    } catch (const std::exception& e) {
        qCritical() << "Failed to load analysis data:" << e.what();
        return AnalysisResults(); // 返回空结果
    }
}
```

## 5. 错误处理策略（精简版）

### 5.1 基础错误处理

```cpp
// 基础验证（假设默认格式验证正确）
class BasicValidation {
public:
    // 基本文件名验证
    static bool isValidFileName(const QString& fileName) {
        return !fileName.isEmpty() && fileName.length() <= 255;
    }

    // 基本数据验证
    static bool isValidPlotData(const PlotDataCollection& plotData) {
        return plotData.isValid() && plotData.calculateSize() > 0;
    }

    // 基本序列验证
    static bool isValidOperationSequence(const OperationSequence& sequence) {
        return !sequence.isEmpty();
    }
};

// 注意：详细的ValidationFramework将在后续版本中实现
// 当前版本假设输入数据格式正确，仅进行基础验证
```

### 5.2 简化异常处理

```cpp
// 简化的异常处理（基于Qt标准实践）
class FileManagementError {
public:
    static void logError(const QString& operation, const QString& error) {
        qCritical() << "FileManagement Error in" << operation << ":" << error;
    }

    static void logWarning(const QString& operation, const QString& warning) {
        qWarning() << "FileManagement Warning in" << operation << ":" << warning;
    }

    static bool handleFileOperation(const QString& operation,
                                   std::function<bool()> func) {
        try {
            return func();
        } catch (const std::exception& e) {
            logError(operation, e.what());
            return false;
        }
    }
};

// 注意：复杂的错误恢复策略将在后续版本中实现
// 当前版本使用Qt标准日志和基础异常处理
```

## 6. 性能优化策略（精简版）

### 6.1 核心性能考虑

```cpp
// 基础性能优化（当前实现）
class BasicPerformanceOptimization {
public:
    // Qt压缩优化
    static QByteArray optimizedCompress(const QByteArray& data) {
        // 使用Qt默认压缩级别，平衡压缩率和速度
        return qCompress(data);
    }

    static QByteArray optimizedDecompress(const QByteArray& data) {
        return qUncompress(data);
    }

    // 文件I/O优化
    static bool optimizedFileWrite(QFile& file, const QByteArray& data) {
        // 使用缓冲写入
        return file.write(data) == data.size();
    }

    static QByteArray optimizedFileRead(QFile& file, qint64 size) {
        // 预分配内存
        QByteArray data;
        data.reserve(size);
        return file.read(size);
    }
};

// 注意：以下高级性能优化功能将在后续版本中实现：
// - 智能缓存管理系统
// - 异步I/O操作
// - 数据预加载策略
// - LRU缓存淘汰算法
// - 内存池管理
// 当前版本专注于基础功能实现和数据正确性
```

## 7. 破坏式集成策略

### 7.1 直接修改现有代码的集成方案

```cpp
// 破坏式重构集成策略（直接修改现有代码）
class DestructiveIntegrationPlan {
public:
    // 1. ProjectFileManager直接增强
    static void enhanceProjectFileManager() {
        // 直接在ProjectFileManager.h/cpp中添加：
        // - 统一文件管理接口
        // - sflp文件范围的操作计数器管理
        // - 文件关系缓存（简化版）
        // - Qt压缩数据管理
        // 删除或重构不兼容的旧接口
    }

    // 2. OpenProjectWidget直接修改
    static void modifyOpenProjectWidget() {
        // 直接在OpenProjectWidget.h/cpp中修改：
        // - 文件树显示逻辑
        // - 虚拟节点支持
        // - 文件类型过滤
        // - 自然排序算法
        // 删除旧的文件显示逻辑
    }

    // 3. AppConfig集成
    static void integrateAppConfig() {
        // 确保AppConfig支持项目前缀配置
        // 默认值"SFL"
        // 删除其他配置管理重复代码
    }

    // 4. 数据结构替换
    static void replaceDataStructures() {
        // 用新的数据结构替换旧的：
        // - PlotDataCollection替换旧的数据结构
        // - OperationSequence替换旧的操作管理
        // - AnalysisResults替换旧的拟合结果
        // 直接删除不兼容的旧结构
    }
};

// 向后兼容性限制（仅.sfd文件读取）
class LimitedBackwardCompatibility {
public:
    // 仅保留.sfd文件读取支持
    static PlotDataCollection loadFromSfdFile(const QString& sfdFile) {
        // 实现.sfd文件读取，转换为新的数据结构
        // 不支持写入.sfd文件
    }

    // 删除其他向后兼容功能
    // - 不支持旧的操作序列格式
    // - 不支持旧的配置迁移
};
```

### 7.2 破坏式实施步骤

1. **直接替换核心组件**
   - 直接修改ProjectFileManager添加新接口
   - 删除不兼容的旧接口

2. **UI层直接修改**
   - 直接修改OpenProjectWidget实现虚拟节点
   - 删除旧的文件树显示逻辑
   - 不保持UI向后兼容性

3. **数据格式强制升级**
   - 新数据一律使用.sflp格式
   - 仅保留.sfd文件读取功能

4. **配置直接迁移**
   - 直接修改AppConfig支持新配置
   - 不保持配置向后兼容性
   - 使用默认值处理缺失配置

## 8. UI集成设计（完善版）

### 8.1 OpenProjectWidget增强实现

```cpp
// 直接在现有OpenProjectWidget类中添加虚拟节点支持
void OpenProjectWidget::refreshFileTreeWithVirtualNodes() {
    treeModel->clear();

    QString projectPath = projectFileManager->getFilePath();
    if (projectPath.isEmpty()) return;

    // 构建根节点
    QStandardItem* rootItem = new QStandardItem(QFileInfo(projectPath).fileName());
    treeModel->appendRow(rootItem);

    // 获取所有SFLP文件
    QDir directory(projectPath);
    QStringList sflpFiles = directory.entryList(QStringList() << "*.sflp", QDir::Files);

    for (const QString& fileName : sflpFiles) {
        QStandardItem* fileItem = new QStandardItem(fileName);
        fileItem->setData(FileType::Project, Qt::UserRole);
        rootItem->appendRow(fileItem);

        // 添加虚拟子节点
        addVirtualChildren(fileItem, fileName);
    }

    // 应用排序和过滤
    applySortingToTree();
    applyFileTypeFilter();
    treeView->expandAll();
}

void OpenProjectWidget::addVirtualChildren(QStandardItem* parentItem,
                                          const QString& sflpFileName) {
    // 获取操作子项
    QStringList operationChildren = projectFileManager->getChildFiles(sflpFileName);

    for (const QString& operationName : operationChildren) {
        FileType fileType = projectFileManager->getFileType(operationName);

        if (fileType == FileType::Operation) {
            QStandardItem* operationItem = createOperationItem(operationName);
            parentItem->appendRow(operationItem);

            // 添加分析子项
            addAnalysisChildren(operationItem, operationName);
        }
    }
}

void OpenProjectWidget::addAnalysisChildren(QStandardItem* parentItem,
                                           const QString& operationName) {
    QStringList analysisChildren = projectFileManager->getChildFiles(operationName);

    for (const QString& analysisName : analysisChildren) {
        FileType fileType = projectFileManager->getFileType(analysisName);

        if (fileType == FileType::DecayAnalysis || fileType == FileType::SpectralAnalysis) {
            QStandardItem* analysisItem = createAnalysisItem(analysisName, fileType);
            parentItem->appendRow(analysisItem);
        }
    }
}

QStandardItem* OpenProjectWidget::createOperationItem(const QString& operationName) {
    QStandardItem* item = new QStandardItem(operationName);
    item->setData(FileType::Operation, Qt::UserRole);
    item->setIcon(QIcon(":/icons/operation.png")); // 操作图标
    return item;
}

QStandardItem* OpenProjectWidget::createAnalysisItem(const QString& analysisName,
                                                     FileType analysisType) {
    QStandardItem* item = new QStandardItem(analysisName);
    item->setData(analysisType, Qt::UserRole);

    if (analysisType == FileType::DecayAnalysis) {
        item->setIcon(QIcon(":/icons/decay_analysis.png"));
    } else {
        item->setIcon(QIcon(":/icons/spectral_analysis.png"));
    }

    return item;
}

void OpenProjectWidget::setFileTypeFilter(TabType currentTab) {
    // 根据当前Tab类型过滤显示
    switch (currentTab) {
    case TabType::Acquire:
        hideItemsByType({FileType::Operation, FileType::DecayAnalysis, FileType::SpectralAnalysis});
        break;
    case TabType::Process:
        hideItemsByType({FileType::DecayAnalysis, FileType::SpectralAnalysis});
        break;
    case TabType::Analysis:
        // 显示所有类型
        showAllItems();
        break;
    }
}

void OpenProjectWidget::applySortingToTree() {
    // 自然排序算法实现
    treeModel->sort(0, Qt::AscendingOrder);
    // 可以实现自定义排序比较器以支持数字序列排序
}
```

## 9. 实施计划（精简版）

### 9.1 三阶段开发规划

#### 阶段1: 核心组件实现 (2周)
**目标**: 实现FileNameManager和ProjectFileManager增强
- 在data目录创建FileNameManager类（简化版）
- 在ProjectFileManager中添加统一文件管理接口
- 实现基础数据结构（OperationSequence、AnalysisResults等）
- 集成AppConfig配置管理（项目前缀支持）

**交付物**:
- data/FileNameManager.h/cpp
- data/UnifiedFileStructures.h/cpp
- ProjectFileManager增强接口
- AppConfig项目前缀支持

#### 阶段2: SFLP文件格式支持 (2周)
**目标**: 实现SFLP文件格式和压缩机制
- 在data目录创建SflpFileManager类
- 集成Qt压缩/解压缩（qCompress/qUncompress）
- 实现数据序列化/反序列化
- 实现sflp文件范围的操作计数器管理

**交付物**:
- data/SflpFileManager.h/cpp
- SFLP文件格式完整支持（包含reserved[16]）
- Qt压缩集成
- 操作计数器管理（按sflp文件）

#### 阶段3: UI层集成 (1-2周)
**目标**: 集成OpenProjectWidget虚拟节点显示
- 直接修改OpenProjectWidget实现虚拟节点显示
- 实现DecAna_{序号}/SpeAna_{序号}命名显示
- 文件类型过滤（按Tab类型）
- 自然排序算法

**交付物**:
- 增强的OpenProjectWidget文件树显示
- 虚拟节点支持（操作和分析子项）
- 文件类型过滤
- 项目文件更新（.pro文件）

