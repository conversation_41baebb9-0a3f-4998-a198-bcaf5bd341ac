# SpecFLIM SFD到SFLP格式迁移设计文档

## 1. 迁移概述

### 1.1 迁移目标
将SpecFLIM应用程序的文件管理系统从SFD格式完全迁移到SFLP格式，实现统一的文件管理架构，同时保持对现有SFD文件的读取兼容性。

### 1.2 关键差异分析

#### SFD格式现状：
- **主文件**: `SpecFlimData_N.sfd` - 包含测量参数和累积数据
- **帧数据文件**: `SpecFlimData_N.sfd.x` - 分散的帧数据文件
- **压缩方式**: Qt压缩（qCompress/qUncompress）
- **数据结构**: MeasureDataHandler的完整状态

#### SFLP格式目标：
- **统一文件**: `SpecFlimData_N.sflp` - 包含所有数据的单一文件
- **数据段管理**: 帧数据作为独立数据段存储
- **索引系统**: 完整的数据段索引和元数据管理
- **扩展性**: 支持操作数据和分析数据的虚拟子项

### 1.3 迁移优势
1. **文件统一**: 消除分散的sfd.x文件，简化文件管理
2. **性能提升**: 通过索引系统提高数据访问效率
3. **扩展性**: 为Process和Analysis功能提供统一的存储基础
4. **维护性**: 简化文件格式，降低维护复杂度

## 2. 数据结构映射设计

### 2.1 MeasureDataHandler数据映射

#### 现有SFD数据结构：
```cpp
// 主文件内容
struct SFDMainData {
    // 文件信息
    QString ident, softwareVersion, hardwareVersion;
    QString fileTime, fileChangeTime, comment;
    QString measurementMode, monoFilter;
    
    // 测量参数
    unsigned int waveChannels, timeChannels;
    float tdcResolution, monoWave;
    std::vector<float> tdcResolutionArray;
    std::vector<float> waveAxisArray, timeAxisArray;
    
    // 累积数据
    std::vector<std::vector<int>> dataArrayTotal;
    
    // 其他参数...
};

// 帧数据文件内容（sfd.x）
struct SFDFrameData {
    qint32 frameCount;
    QList<DataFrame> frames;
};
```

#### 目标SFLP数据段结构：
```cpp
// SFLP主数据段："measurement_metadata"
struct SflpMeasurementMetadata {
    // 完全保持MeasureDataHandler的所有字段
    QString ident, softwareVersion, hardwareVersion;
    QString fileTime, fileChangeTime, comment;
    QString measurementMode, monoFilter;
    unsigned int waveChannels, timeChannels;
    float tdcResolution, monoWave;
    QVector<float> tdcResolutionArray;  // Qt类型
    QVector<float> waveAxisArray, timeAxisArray;  // Qt类型
    QVector<QVector<int>> dataArrayTotal;  // Qt类型
    // ... 所有其他字段保持不变
};

// SFLP帧数据段："frame_data_N"
struct SflpFrameDataSegment {
    qint32 frameCount;
    QVector<DataFrame> frames;  // Qt类型
};
```

### 2.2 数据段命名规范

```cpp
// 数据段命名规范
class SflpDataSegmentNames {
public:
    static const QString MEASUREMENT_METADATA = "measurement_metadata";
    static const QString FRAME_DATA_PREFIX = "frame_data_";  // frame_data_1, frame_data_2, ...
    static const QString MEASUREMENT_CONFIG = "measurement_config";
    static const QString AXIS_DATA = "axis_data";
    
    // 生成帧数据段名称
    static QString generateFrameDataSegmentName(int segmentIndex) {
        return QString("%1%2").arg(FRAME_DATA_PREFIX).arg(segmentIndex);
    }
};
```

## 3. ProjectFileManager重构设计

### 3.1 SFLP格式支持扩展

```cpp
class ProjectFileManager : public QObject {
    Q_OBJECT

public:
    // 现有接口保持不变...
    
    // 新增：SFLP格式测量数据管理
    bool saveMeasurementDataToSflp(MeasureDataHandler* handler);
    bool loadMeasurementDataFromSflp(const QString& sflpFileName, 
                                    MeasureDataHandler* handler);
    
    // 新增：SFD到SFLP转换
    bool convertSfdToSflp(const QString& sfdFileName, 
                         const QString& sflpFileName);
    
    // 新增：帧数据管理
    bool saveFrameDataToSflp(const QString& sflpFileName,
                            const QVector<DataFrame>& frames,
                            int segmentIndex);
    QVector<DataFrame> loadFrameDataFromSflp(const QString& sflpFileName,
                                            int segmentIndex);

private:
    // 新增：SFLP文件管理器缓存
    QMap<QString, QSharedPointer<SflpFileManager>> m_sflpManagers;
    mutable QMutex m_managerMutex;
    
    // 内部方法
    QSharedPointer<SflpFileManager> getSflpManager(const QString& fileName);
    void releaseSflpManager(const QString& fileName);
    
    // 数据转换方法
    QByteArray serializeMeasurementMetadata(MeasureDataHandler* handler);
    bool deserializeMeasurementMetadata(const QByteArray& data, 
                                       MeasureDataHandler* handler);
    QByteArray serializeFrameData(const QVector<DataFrame>& frames);
    QVector<DataFrame> deserializeFrameData(const QByteArray& data);
};
```

### 3.2 测量流程重构

```cpp
// 重构后的测量流程
void ProjectFileManager::startMeasurement() {
    QDir projectDir(m_projectPath);
    QFile file(projectDir.filePath("metadata.sfp"));
    if (!file.exists()) {
        saveMetadataFile();
    }

    qDebug() << "Starting measurement with SFLP format";
    clearCurrentHandler();

    m_lastDataFileNumber++;
    // 修改：使用SFLP格式
    QString fileName = AppConfig::instance()->readConfig("DataFileName", "SpecFlimData").toString()
        + "_" + QString::number(m_lastDataFileNumber) + ".sflp";

    m_currentHandler = new MeasureDataHandler();
    m_currentHandler->start(projectDir.filePath(fileName));
    
    // 修改：保存为SFLP格式
    saveMeasurementDataToSflp(m_currentHandler);

    m_fileHandlers.insert(fileName, m_currentHandler);
    emit fileHandlersChanged();
}
```

## 4. MeasureDataHandler适配设计

### 4.1 保存机制重构

```cpp
class MeasureDataHandler : public QObject {
    // 现有接口保持不变...

public:
    // 新增：SFLP格式支持
    bool saveToSflpFormat(const QString& sflpFileName);
    bool loadFromSflpFormat(const QString& sflpFileName);
    
    // 修改：帧数据保存策略
    void saveDataArrayToSflpFile();  // 替代原有的saveDataArrayToFile

private:
    // 新增：SFLP相关成员
    QString m_sflpFileName;  // 当前SFLP文件名
    int m_currentFrameSegment;  // 当前帧数据段索引
    
    // 修改：异步保存到SFLP
    void saveDataArrayToSflpAsync(const QVector<DataFrame>& dataArrayCopy);
    
    // 数据转换方法
    QByteArray serializeToSflpMetadata() const;
    bool deserializeFromSflpMetadata(const QByteArray& data);
};
```

### 4.2 兼容性保证

```cpp
// 保持现有接口的完全兼容性
bool MeasureDataHandler::saveProject() {
    // 检查文件扩展名决定保存格式
    QFileInfo fileInfo(filePath);
    if (fileInfo.suffix().toLower() == "sflp") {
        return saveToSflpFormat(filePath);
    } else {
        // 保持原有SFD保存逻辑用于兼容性
        return saveProjectLegacySfd();
    }
}

bool MeasureDataHandler::openProject(const QString& filePath) {
    QFileInfo fileInfo(filePath);
    if (fileInfo.suffix().toLower() == "sflp") {
        return loadFromSflpFormat(filePath);
    } else {
        // 保持原有SFD读取逻辑
        return openProjectLegacySfd(filePath);
    }
}
```

## 5. SFD到SFLP转换功能设计

### 5.1 转换器类设计

```cpp
class SfdToSflpConverter : public QObject {
    Q_OBJECT

public:
    explicit SfdToSflpConverter(QObject* parent = nullptr);

    // 主转换接口
    bool convertFile(const QString& sfdFilePath, const QString& sflpFilePath);
    bool convertProject(const QString& projectPath);  // 转换整个项目

    // 批量转换
    QStringList convertMultipleFiles(const QStringList& sfdFiles,
                                    const QString& outputDir);

    // 验证和检查
    bool validateSfdFile(const QString& sfdFilePath);
    bool validateConversion(const QString& sfdFilePath, const QString& sflpFilePath);

    // 进度和状态
    int getProgress() const { return m_progress; }
    QString getLastError() const { return m_lastError; }

signals:
    void progressChanged(int percentage);
    void conversionCompleted(const QString& sflpFilePath);
    void errorOccurred(const QString& error);

private:
    int m_progress;
    QString m_lastError;

    // 内部转换方法
    bool convertMainData(const QString& sfdFile, SflpFileManager* sflpManager);
    bool convertFrameData(const QString& sfdFile, SflpFileManager* sflpManager);
    QStringList findFrameDataFiles(const QString& sfdFile);

    // 数据处理
    QByteArray loadSfdMainData(const QString& sfdFile);
    QVector<DataFrame> loadSfdFrameData(const QString& frameFile);
    bool saveSflpData(SflpFileManager* manager, const QString& segmentName,
                     const QByteArray& data);
};
```

### 5.2 转换流程设计

```cpp
// 转换流程实现
bool SfdToSflpConverter::convertFile(const QString& sfdFilePath,
                                    const QString& sflpFilePath) {
    try {
        // 1. 验证输入文件
        if (!validateSfdFile(sfdFilePath)) {
            m_lastError = "Invalid SFD file format";
            return false;
        }

        // 2. 创建SFLP文件管理器
        auto sflpManager = QSharedPointer<SflpFileManager>::create(sflpFilePath);
        if (!sflpManager->openFile(QIODevice::WriteOnly)) {
            m_lastError = "Failed to create SFLP file";
            return false;
        }

        // 3. 转换主数据
        m_progress = 20;
        emit progressChanged(m_progress);
        if (!convertMainData(sfdFilePath, sflpManager.data())) {
            return false;
        }

        // 4. 转换帧数据
        m_progress = 50;
        emit progressChanged(m_progress);
        if (!convertFrameData(sfdFilePath, sflpManager.data())) {
            return false;
        }

        // 5. 验证转换结果
        m_progress = 90;
        emit progressChanged(m_progress);
        if (!validateConversion(sfdFilePath, sflpFilePath)) {
            m_lastError = "Conversion validation failed";
            return false;
        }

        m_progress = 100;
        emit progressChanged(m_progress);
        emit conversionCompleted(sflpFilePath);
        return true;

    } catch (const std::exception& e) {
        m_lastError = QString("Conversion failed: %1").arg(e.what());
        emit errorOccurred(m_lastError);
        return false;
    }
}
```

## 6. 错误处理和验证设计

### 6.1 数据完整性验证

```cpp
class SflpDataValidator {
public:
    // 验证SFLP文件完整性
    static bool validateSflpFile(const QString& sflpFilePath);

    // 验证测量数据完整性
    static bool validateMeasurementData(const SflpMeasurementMetadata& metadata);

    // 验证帧数据完整性
    static bool validateFrameData(const QVector<DataFrame>& frames,
                                 int expectedWaveChannels,
                                 int expectedTimeChannels);

    // 比较SFD和SFLP数据一致性
    static bool compareSfdSflpData(const QString& sfdFile, const QString& sflpFile);

private:
    static bool validateDataDimensions(const QVector<QVector<int>>& data,
                                      int expectedWave, int expectedTime);
    static bool validateAxisArrays(const QVector<float>& waveAxis,
                                  const QVector<float>& timeAxis,
                                  int waveChannels, int timeChannels);
};
```

### 6.2 错误恢复机制

```cpp
class SflpErrorRecovery {
public:
    // 修复损坏的SFLP文件
    static bool repairSflpFile(const QString& sflpFilePath);

    // 从备份恢复数据
    static bool restoreFromBackup(const QString& sflpFilePath,
                                 const QString& backupPath);

    // 重建索引
    static bool rebuildSflpIndex(const QString& sflpFilePath);

    // 数据段恢复
    static bool recoverDataSegment(const QString& sflpFilePath,
                                  const QString& segmentName);

private:
    static bool validateAndFixHeader(SflpFileManager* manager);
    static bool validateAndFixIndex(SflpFileManager* manager);
    static QByteArray attemptDataRecovery(const QByteArray& corruptedData);
};
```

## 7. 实施计划

### 7.1 第一阶段：核心转换功能（2周）
**目标**: 实现基础的SFD到SFLP转换功能

**任务**:
1. 创建SfdToSflpConverter类
2. 实现MeasureDataHandler的SFLP格式支持
3. 扩展ProjectFileManager的SFLP管理功能
4. 实现基础的数据验证机制

**交付物**:
- `data/SfdToSflpConverter.h/cpp`
- `MeasureDataHandler`的SFLP支持方法
- `ProjectFileManager`的SFLP扩展接口
- 基础单元测试

### 7.2 第二阶段：测量流程集成（1周）
**目标**: 将SFLP格式集成到测量流程中

**任务**:
1. 修改`startMeasurement()`使用SFLP格式
2. 更新帧数据保存机制
3. 实现SFD文件的读取兼容性
4. 测试测量数据的完整性

**交付物**:
- 更新的测量流程代码
- SFD兼容性读取功能
- 集成测试用例

### 7.3 第三阶段：错误处理和优化（1周）
**目标**: 完善错误处理和性能优化

**任务**:
1. 实现完整的错误处理机制
2. 添加数据验证和恢复功能
3. 性能优化和内存管理
4. 用户界面集成测试

**交付物**:
- 完整的错误处理系统
- 性能优化代码
- 完整的测试套件
- 用户文档更新

## 8. 技术约束和要求

### 8.1 Qt最佳实践要求
- 使用QVector、QMap、QSharedPointer等Qt类型
- 遵循Qt内存管理模式
- 使用Qt信号槽机制进行事件通知
- 所有用户界面消息使用英语

### 8.2 生产就绪要求
- 完整的错误处理和参数验证
- 不允许TODO注释或占位符代码
- 完整的API实现，包括边界情况处理
- 异步文件I/O操作，避免界面阻塞

### 8.3 兼容性要求
- 保持MeasureDataHandler现有功能100%兼容
- 支持现有SFD文件的读取
- 新数据仅保存为SFLP格式
- 保持与Process和Analysis模块的接口兼容性
