# SpecFLIM SFD到SFLP格式迁移设计文档（简化版）

## 1. 迁移概述

### 1.1 迁移目标
将SpecFLIM应用程序完全迁移到SFLP格式，通过自动转换机制消除SFD格式依赖，简化架构并提高维护性。

### 1.2 关键简化策略

#### 消除metadata.sfp依赖：
- **现状分析**: metadata.sfp仅存储`m_currentVersion`和`m_lastDataFileNumber`两个简单值
- **SFLP替代**: 将项目元数据存储在主SFLP文件的专用数据段中
- **优势**: 消除额外文件依赖，统一项目数据管理

#### 自动转换策略：
- **触发时机**: 在`openProject()`检测到SFD文件时自动转换
- **静默转换**: 后台异步转换，不显示进度给用户
- **文件替换**: 转换完成后直接替换原SFD文件
- **简化维护**: 消除长期双格式支持的复杂性

#### MeasureDataHandler重构评估：
- **保留功能**: 测量状态管理、实时数据处理、内存优化
- **移除功能**: 文件I/O操作完全移交给SflpFileManager
- **简化接口**: 专注于测量逻辑，不再处理文件格式细节

### 1.3 架构简化优势
1. **单一格式**: 完全消除SFD格式，仅使用SFLP
2. **自动迁移**: 用户无感知的格式升级
3. **维护简化**: 无需维护复杂的兼容性层
4. **性能提升**: 统一的文件管理和索引系统

## 2. 简化的数据结构设计

### 2.1 项目元数据简化

#### 基于分析的ProjectMetadata属性决策：

**m_currentVersion**: **保留**
- 用于版本控制和快照管理功能
- 低频使用但对高级功能重要
- 作为项目级别元数据存储在主SFLP文件中

**m_lastDataFileNumber**: **移除，移交给FileNameManager**
- 当前仅用于测量文件名序列号管理
- FileNameManager已有类似功能（getNextProjectSequence）
- 统一序列号管理，避免职责重复

#### 简化后的ProjectMetadata：
```cpp
// 项目元数据数据段："project_metadata"
struct ProjectMetadata {
    int currentVersion;  // 保留：版本控制功能
    QString projectName;
    QDateTime createdTime;
    QDateTime lastModifiedTime;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};
```

#### FileNameManager扩展：
```cpp
class FileNameManager {
public:
    // 新增：测量数据文件序列号管理
    int getNextMeasurementSequence(const QString& projectPath) const;
    QString generateMeasurementFileName(const QString& projectPath) const;

private:
    // 统一的序列号管理
    int getNextSequenceForProject(const QString& projectPath, const QString& filePattern) const;
};
```

### 2.2 MeasureDataHandler简化

#### 保留的核心功能：
```cpp
class MeasureDataHandler : public QObject {
    Q_OBJECT

public:
    // 测量状态管理
    void start(const QString& sflpFileName);
    void stop();
    bool isSaving() const;

    // 实时数据处理
    void createAndAppendDataFrame(quint16 frameNumber, quint32 pulseCount,
                                 const QVector<QVector<int>>& data);
    void checkAndSaveData();

    // 测量参数访问（只读）
    unsigned int getWaveChannels() const { return waveChannels; }
    unsigned int getTimeChannels() const { return timeChannels; }
    const QVector<float>& getWaveAxisArray() const { return waveAxisArray; }
    const QVector<float>& getTimeAxisArray() const { return timeAxisArray; }

    // 累积数据访问
    const QVector<QVector<int>>& getDataArrayTotal() const { return dataArrayTotal; }

private:
    // 测量状态
    QString m_sflpFileName;
    bool saving;
    qint64 startTime, endTime;

    // 测量参数（从SFLP加载）
    unsigned int waveChannels, timeChannels;
    QVector<float> waveAxisArray, timeAxisArray;
    QVector<QVector<int>> dataArrayTotal;

    // 实时帧数据缓存
    QVector<DataFrame> dataArray;
    QTimer* m_saveTimer;

    // 移除所有文件I/O相关代码
    // 所有保存/加载操作通过ProjectFileManager调用SflpFileManager
};
```

### 2.3 SFLP数据段规范

```cpp
// 简化的数据段命名
class SflpSegmentNames {
public:
    static const QString PROJECT_METADATA = "project_metadata";
    static const QString MEASUREMENT_DATA = "measurement_data";
    static const QString FRAME_DATA_PREFIX = "frame_data_";

    static QString frameDataSegment(int index) {
        return QString("%1%2").arg(FRAME_DATA_PREFIX).arg(index);
    }
};
```

## 3. 文件管理架构集成评估

### 3.1 当前集成状态分析

#### **已集成的类**：

**SflpFileManager**: ✅ **完全集成**
- 在ProjectFileManager中有完整的缓存管理（`m_sflpManagers`）
- 通过`getSflpManager()`和`releaseSflpManager()`管理生命周期
- 在`writeDataSegmentToSflp()`和`readDataSegmentFromSflp()`中使用

**FileNameManager**: ✅ **部分集成**
- 在`saveOperationData()`和`saveAnalysisData()`中使用
- 用于生成操作名称和分析名称
- **缺失**: 测量数据文件名生成仍在ProjectFileManager中硬编码

#### **未集成的类**：

**FileRelationshipCache**: ❌ **未集成**
- ProjectFileManager有自己的关系管理（`m_childrenMap`、`m_parentMap`）
- 功能重复，应该统一使用FileRelationshipCache

**VirtualNodeManager**: ❌ **未集成**
- 完全独立于ProjectFileManager
- 应该通过ProjectFileManager获取文件关系信息

**SflpFileOperations**: ❌ **未集成**
- ProjectFileManager直接使用SflpFileManager
- SflpFileOperations提供更高级的操作，应该替代直接调用

### 3.2 集成缺陷和建议

#### **问题1：文件关系管理重复**
```cpp
// 当前ProjectFileManager中的重复实现
QMap<QString, QStringList> m_childrenMap; // 父文件→子项列表
QMap<QString, QString> m_parentMap;       // 子项→父文件

// 应该使用现有的FileRelationshipCache
void updateFileRelationship(const QString& parent, const QString& child) {
    // 当前实现重复了FileRelationshipCache的功能
}
```

**建议**: 移除ProjectFileManager中的关系管理，统一使用FileRelationshipCache

#### **问题2：SflpFileOperations未被利用**
```cpp
// 当前ProjectFileManager直接使用SflpFileManager
bool success = manager->writeDataSegment(segmentName, data);

// 应该使用SflpFileOperations的高级接口
bool success = sflpOps->writeOperationData(sflpFileName, operationName, plotData);
```

**建议**: 使用SflpFileOperations替代直接的SflpFileManager调用

#### **问题3：FileNameManager集成不完整**
```cpp
// 当前startMeasurement()中的硬编码
QString fileName = AppConfig::instance()->readConfig("DataFileName", "SpecFlimData").toString()
    + "_" + QString::number(m_lastDataFileNumber) + ".sfd";

// 应该使用FileNameManager
QString fileName = FileNameManager::getInstance()->generateMeasurementFileName(m_projectPath);
```

### 3.3 统一集成架构设计

#### **重构后的ProjectFileManager**：
```cpp
class ProjectFileManager : public QObject {
    Q_OBJECT

public:
    // 简化的项目管理接口
    void createNewProject(const QString& projectPath);
    void openProject(const QString& projectPath);
    void saveProject();

    // 测量管理（使用FileNameManager）
    void startMeasurement();
    void stopMeasurement();

private:
    QString m_projectPath;
    QMap<QString, MeasureDataHandler*> m_fileHandlers;

    // 简化的项目元数据（移除m_lastDataFileNumber）
    int m_currentVersion;

    // 统一的文件管理组件
    FileRelationshipCache* m_relationshipCache;
    FileNameManager* m_fileNameManager;
    SflpFileOperations* m_sflpOperations;

    // 移除重复的关系管理成员
    // QMap<QString, QStringList> m_childrenMap;  // 删除
    // QMap<QString, QString> m_parentMap;        // 删除

    // 自动转换相关
    void autoConvertSfdFiles(const QString& projectPath);
    bool convertSfdToSflpSilently(const QString& sfdFile);
};
```

## 4. 具体集成建议

### 4.1 ProjectFileManager重构建议

#### **第一步：移除重复的文件关系管理**
```cpp
class ProjectFileManager : public QObject {
private:
    // 移除重复实现
    // QMap<QString, QStringList> m_childrenMap;  // 删除
    // QMap<QString, QString> m_parentMap;        // 删除
    // QMutex m_relationMutex;                    // 删除

    // 使用统一的文件管理组件
    FileRelationshipCache* m_relationshipCache;

    // 更新文件关系的方法
    void updateFileRelationship(const QString& parent, const QString& child) {
        m_relationshipCache->addRelationship(parent, child);
    }
};
```

#### **第二步：集成SflpFileOperations**
```cpp
class ProjectFileManager : public QObject {
private:
    SflpFileOperations* m_sflpOperations;

    // 替代直接的SflpFileManager调用
    bool saveOperationData(const QString& sflpFileName,
                          const OperationSequence& sequence,
                          const PlotDataCollection& plotData) {
        return m_sflpOperations->writeOperationData(sflpFileName,
                                                   generateOperationName(sequence),
                                                   plotData);
    }
};
```

#### **第三步：完整集成FileNameManager**
```cpp
class ProjectFileManager : public QObject {
private:
    // 移除m_lastDataFileNumber，使用FileNameManager
    // int m_lastDataFileNumber;  // 删除

    void startMeasurement() {
        // 使用FileNameManager生成文件名
        QString fileName = m_fileNameManager->generateMeasurementFileName(m_projectPath);

        m_currentHandler = new MeasureDataHandler();
        m_currentHandler->start(QDir(m_projectPath).filePath(fileName));

        m_fileHandlers.insert(fileName, m_currentHandler);
        emit fileHandlersChanged();
    }
};
```

### 4.2 VirtualNodeManager集成建议

#### **通过ProjectFileManager提供数据接口**
```cpp
class ProjectFileManager : public QObject {
public:
    // 为VirtualNodeManager提供文件关系数据
    QStringList getChildFiles(const QString& parentFile) const {
        return m_relationshipCache->getChildren(parentFile);
    }

    QString getParentFile(const QString& childFile) const {
        return m_relationshipCache->getParent(childFile);
    }

    QStringList getAllProjectFiles() const {
        return m_relationshipCache->getAllParents();
    }

signals:
    // 通知VirtualNodeManager更新
    void fileRelationshipChanged(const QString& parent, const QString& child);
    void fileAdded(const QString& fileName);
    void fileRemoved(const QString& fileName);
};
```

#### **VirtualNodeManager连接ProjectFileManager**
```cpp
class VirtualNodeManager : public QObject {
public:
    void connectToProjectManager(ProjectFileManager* projectManager) {
        connect(projectManager, &ProjectFileManager::fileRelationshipChanged,
                this, &VirtualNodeManager::onFileRelationshipChanged);
        connect(projectManager, &ProjectFileManager::fileAdded,
                this, &VirtualNodeManager::onFileAdded);
        connect(projectManager, &ProjectFileManager::fileRemoved,
                this, &VirtualNodeManager::onFileRemoved);
    }

private slots:
    void onFileRelationshipChanged(const QString& parent, const QString& child);
    void onFileAdded(const QString& fileName);
    void onFileRemoved(const QString& fileName);
};
```

## 5. 简化的转换器设计

### 5.1 最小化SfdToSflpConverter

```cpp
class SfdToSflpConverter {
public:
    // 简化的转换接口
    static bool convertFile(const QString& sfdFilePath, const QString& sflpFilePath);

    // 静默转换（无进度显示）
    static bool convertSilently(const QString& sfdFilePath, const QString& sflpFilePath);

private:
    // 基础转换方法
    static bool loadSfdData(const QString& sfdFile,
                           QByteArray& mainData,
                           QVector<QByteArray>& frameDataList);
    static bool saveSflpData(const QString& sflpFile,
                            const QByteArray& mainData,
                            const QVector<QByteArray>& frameDataList);

    // 移除复杂的错误处理和进度管理
    // 移除批量转换功能
    // 移除验证功能
};
```

### 4.2 转换流程简化

```cpp
// 简化的转换实现
bool SfdToSflpConverter::convertSilently(const QString& sfdFilePath,
                                         const QString& sflpFilePath) {
    try {
        // 1. 加载SFD数据
        QByteArray mainData;
        QVector<QByteArray> frameDataList;
        if (!loadSfdData(sfdFilePath, mainData, frameDataList)) {
            return false;
        }

        // 2. 保存为SFLP格式
        if (!saveSflpData(sflpFilePath, mainData, frameDataList)) {
            return false;
        }

        return true;

    } catch (...) {
        // 简单的异常处理
        return false;
    }
}
```

## 5. 简化的实施计划

### 5.1 第一阶段：核心转换功能（1周）

**目标**: 实现基础的SFD到SFLP转换和项目元数据整合

**任务**:
1. **创建简化的SfdToSflpConverter类**
   - 文件: `data/SfdToSflpConverter.h/cpp`
   - 仅实现基础转换功能，无复杂错误处理
   - 静态方法，无信号槽

2. **消除metadata.sfp依赖**
   - 修改ProjectFileManager，移除saveMetadataFile()
   - 实现项目元数据到SFLP的存储
   - 创建ProjectMetadata结构

3. **实现自动转换机制**
   - 修改openProject()方法
   - 添加autoConvertSfdFiles()方法
   - 静默转换，无用户交互

**验收标准**:
- 能够静默转换SFD文件到SFLP格式
- 项目元数据存储在SFLP文件中
- 自动转换机制正常工作

### 5.2 第二阶段：MeasureDataHandler简化（1周）

**目标**: 简化MeasureDataHandler，移除文件I/O功能

**任务**:
1. **重构MeasureDataHandler**
   - 移除所有saveProject()和openProject()相关代码
   - 保留测量状态管理和实时数据处理
   - 使用Qt类型替代STL类型

2. **集成SFLP保存机制**
   - 修改startMeasurement()使用SFLP格式
   - 通过ProjectFileManager调用SflpFileManager
   - 移除sfd.x文件生成逻辑

3. **测试和验证**
   - 确保测量流程正常工作
   - 验证数据完整性
   - 性能测试

**验收标准**:
- MeasureDataHandler专注于测量逻辑
- 所有文件操作通过SflpFileManager
- 测量数据正确保存到SFLP格式

### 5.3 第三阶段：清理和优化（0.5周）

**目标**: 清理旧代码，优化性能

**任务**:
1. **移除旧代码**
   - 删除SFD格式相关的保存代码
   - 移除metadata.sfp相关代码
   - 清理不再使用的方法

2. **性能优化**
   - 优化SFLP文件管理器缓存
   - 内存使用优化
   - 异步I/O优化

3. **最终测试**
   - 完整的端到端测试
   - 性能基准测试
   - 稳定性测试

**验收标准**:
- 代码简洁，无冗余
- 性能达到或超过原有水平
- 所有功能测试通过

## 6. 技术约束和要求

### 6.1 简化原则
- **最小化复杂性**: 移除SflpErrorRecovery类，依赖SflpFileManager的基础错误处理
- **自动化优先**: 用户无感知的自动转换，无需复杂的进度管理
- **单一职责**: 每个类专注于核心功能，避免功能重叠

### 6.2 Qt最佳实践要求
- 使用QVector、QMap、QSharedPointer等Qt类型
- 遵循Qt内存管理模式
- 所有用户界面消息使用英语
- 异步文件I/O操作，避免界面阻塞

### 6.3 生产就绪要求
- 完整的错误处理和参数验证
- 不允许TODO注释或占位符代码
- 完整的API实现，包括边界情况处理
- 保持MeasureDataHandler现有功能100%兼容

### 6.4 架构简化要求
- **消除metadata.sfp**: 项目元数据完全整合到SFLP文件中
- **自动转换**: openProject()时自动静默转换SFD文件
- **单一格式**: 新数据仅保存为SFLP格式，完全停用SFD格式
- **功能分离**: MeasureDataHandler专注测量逻辑，文件I/O完全由SflpFileManager处理

## 7. 总结

### 7.1 关键简化决策
1. **metadata.sfp消除**: 分析确认该文件仅存储两个简单值，完全可以整合到SFLP文件中
2. **自动转换策略**: 静默转换消除用户交互复杂性，直接替换文件简化维护
3. **MeasureDataHandler重构**: 保留测量核心功能，移除文件I/O职责
4. **转换器简化**: 最小化功能，移除复杂的错误处理和进度管理

### 7.2 实施时间线
- **总计2.5周**: 相比原计划4周大幅缩短
- **第一阶段1周**: 核心转换和元数据整合
- **第二阶段1周**: MeasureDataHandler简化
- **第三阶段0.5周**: 清理和优化

### 7.3 预期收益
1. **维护简化**: 消除双格式支持的复杂性
2. **用户体验**: 无感知的自动升级
3. **代码质量**: 更清晰的职责分离
4. **性能提升**: 统一的文件管理和索引系统
