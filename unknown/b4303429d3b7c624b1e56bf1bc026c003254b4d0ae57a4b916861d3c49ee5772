#ifndef SFDTOSFLPCONVERTER_H
#define SFDTOSFLPCONVERTER_H

#include <QString>
#include <QByteArray>
#include <QVector>

class SflpFileManager;
struct DataFrame;

/**
 * @brief 简化的SFD到SFLP转换器
 * 
 * 提供静态方法进行SFD格式到SFLP格式的转换
 * 设计原则：最小化、专注核心功能、无复杂错误处理
 */
class SfdToSflpConverter
{
public:
    /**
     * @brief 转换SFD文件到SFLP格式
     * @param sfdFilePath SFD文件路径
     * @param sflpFilePath 目标SFLP文件路径
     * @return 转换是否成功
     */
    static bool convertFile(const QString& sfdFilePath, const QString& sflpFilePath);
    
    /**
     * @brief 静默转换SFD文件（无进度显示）
     * @param sfdFilePath SFD文件路径
     * @param sflpFilePath 目标SFLP文件路径
     * @return 转换是否成功
     */
    static bool convertSilently(const QString& sfdFilePath, const QString& sflpFilePath);

private:
    /**
     * @brief 加载SFD主文件数据
     * @param sfdFile SFD文件路径
     * @param mainData 输出的主数据
     * @return 加载是否成功
     */
    static bool loadSfdMainData(const QString& sfdFile, QByteArray& mainData);
    
    /**
     * @brief 加载SFD帧数据文件
     * @param sfdFile SFD文件路径
     * @param frameDataList 输出的帧数据列表
     * @return 加载是否成功
     */
    static bool loadSfdFrameData(const QString& sfdFile, QVector<QByteArray>& frameDataList);
    
    /**
     * @brief 保存数据到SFLP文件
     * @param sflpFile SFLP文件路径
     * @param mainData 主数据
     * @param frameDataList 帧数据列表
     * @return 保存是否成功
     */
    static bool saveSflpData(const QString& sflpFile,
                            const QByteArray& mainData,
                            const QVector<QByteArray>& frameDataList);
    
    /**
     * @brief 查找相关的帧数据文件
     * @param sfdFile SFD文件路径
     * @return 帧数据文件路径列表
     */
    static QStringList findFrameDataFiles(const QString& sfdFile);
    
    /**
     * @brief 验证SFD文件格式
     * @param sfdFile SFD文件路径
     * @return 文件格式是否有效
     */
    static bool validateSfdFile(const QString& sfdFile);
};

#endif // SFDTOSFLPCONVERTER_H
