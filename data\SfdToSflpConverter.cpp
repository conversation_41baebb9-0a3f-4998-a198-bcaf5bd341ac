#include "SfdToSflpConverter.h"
#include "SflpFileManager.h"
#include "UnifiedFileStructures.h"
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDataStream>
#include <QDebug>
#include <QRegularExpression>

bool SfdToSflpConverter::convertFile(const QString& sfdFilePath, const QString& sflpFilePath)
{
    return convertSilently(sfdFilePath, sflpFilePath);
}

bool SfdToSflpConverter::convertSilently(const QString& sfdFilePath, const QString& sflpFilePath)
{
    if (sfdFilePath.isEmpty() || sflpFilePath.isEmpty()) {
        qWarning() << "Invalid file paths for conversion";
        return false;
    }
    
    try {
        // 1. 验证SFD文件
        if (!validateSfdFile(sfdFilePath)) {
            qWarning() << "Invalid SFD file format:" << sfdFilePath;
            return false;
        }
        
        // 2. 加载SFD数据
        QByteArray mainData;
        QVector<QByteArray> frameDataList;
        
        if (!loadSfdMainData(sfdFilePath, mainData)) {
            qWarning() << "Failed to load SFD main data:" << sfdFilePath;
            return false;
        }
        
        if (!loadSfdFrameData(sfdFilePath, frameDataList)) {
            qWarning() << "Failed to load SFD frame data:" << sfdFilePath;
            return false;
        }
        
        // 3. 保存为SFLP格式
        if (!saveSflpData(sflpFilePath, mainData, frameDataList)) {
            qWarning() << "Failed to save SFLP data:" << sflpFilePath;
            return false;
        }
        
        qDebug() << "Successfully converted" << sfdFilePath << "to" << sflpFilePath;
        return true;
        
    } catch (...) {
        qCritical() << "Exception during conversion:" << sfdFilePath << "to" << sflpFilePath;
        return false;
    }
}

bool SfdToSflpConverter::loadSfdMainData(const QString& sfdFile, QByteArray& mainData)
{
    QFile file(sfdFile);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open SFD file for reading:" << sfdFile;
        return false;
    }
    
    mainData = file.readAll();
    file.close();
    
    if (mainData.isEmpty()) {
        qWarning() << "SFD file is empty:" << sfdFile;
        return false;
    }
    
    return true;
}

bool SfdToSflpConverter::loadSfdFrameData(const QString& sfdFile, QVector<QByteArray>& frameDataList)
{
    frameDataList.clear();
    
    // 查找所有相关的帧数据文件
    QStringList frameFiles = findFrameDataFiles(sfdFile);
    
    for (const QString& frameFile : frameFiles) {
        QFile file(frameFile);
        if (!file.open(QIODevice::ReadOnly)) {
            qWarning() << "Cannot open frame data file:" << frameFile;
            continue;
        }
        
        QByteArray frameData = file.readAll();
        file.close();
        
        if (!frameData.isEmpty()) {
            frameDataList.append(frameData);
        }
    }
    
    qDebug() << "Loaded" << frameDataList.size() << "frame data files for" << sfdFile;
    return true;
}

bool SfdToSflpConverter::saveSflpData(const QString& sflpFile,
                                     const QByteArray& mainData,
                                     const QVector<QByteArray>& frameDataList)
{
    // 创建SFLP文件管理器
    SflpFileManager manager(sflpFile);
    
    if (!manager.openFile(QIODevice::WriteOnly)) {
        qWarning() << "Failed to create SFLP file:" << sflpFile;
        return false;
    }
    
    try {
        // 保存主数据段
        if (!manager.writeDataSegment("measurement_data", mainData)) {
            qWarning() << "Failed to write main data segment";
            return false;
        }
        
        // 保存帧数据段
        for (int i = 0; i < frameDataList.size(); ++i) {
            QString segmentName = QString("frame_data_%1").arg(i + 1);
            if (!manager.writeDataSegment(segmentName, frameDataList[i])) {
                qWarning() << "Failed to write frame data segment:" << segmentName;
                return false;
            }
        }
        
        manager.closeFile();
        qDebug() << "Successfully saved SFLP data with" << frameDataList.size() << "frame segments";
        return true;
        
    } catch (...) {
        qCritical() << "Exception while saving SFLP data";
        manager.closeFile();
        return false;
    }
}

QStringList SfdToSflpConverter::findFrameDataFiles(const QString& sfdFile)
{
    QStringList frameFiles;
    QFileInfo sfdInfo(sfdFile);
    QDir dir = sfdInfo.dir();
    QString baseName = sfdInfo.completeBaseName();
    
    // 查找格式为 "basename.sfd.N" 的文件
    QRegularExpression re(QString("%1\\.sfd\\.(\\d+)$").arg(QRegularExpression::escape(baseName)));
    
    QStringList allFiles = dir.entryList(QDir::Files);
    for (const QString& fileName : allFiles) {
        QRegularExpressionMatch match = re.match(fileName);
        if (match.hasMatch()) {
            frameFiles.append(dir.filePath(fileName));
        }
    }
    
    // 按序号排序
    std::sort(frameFiles.begin(), frameFiles.end(), [&re](const QString& a, const QString& b) {
        QRegularExpressionMatch matchA = re.match(QFileInfo(a).fileName());
        QRegularExpressionMatch matchB = re.match(QFileInfo(b).fileName());
        
        if (matchA.hasMatch() && matchB.hasMatch()) {
            return matchA.captured(1).toInt() < matchB.captured(1).toInt();
        }
        return a < b;
    });
    
    return frameFiles;
}

bool SfdToSflpConverter::validateSfdFile(const QString& sfdFile)
{
    if (sfdFile.isEmpty()) {
        return false;
    }
    
    QFileInfo info(sfdFile);
    if (!info.exists() || !info.isFile()) {
        return false;
    }
    
    if (info.suffix().toLower() != "sfd") {
        return false;
    }
    
    if (info.size() == 0) {
        return false;
    }
    
    return true;
}
