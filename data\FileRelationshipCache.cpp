#include "FileRelationshipCache.h"
#include "FileNameManager.h"
#include <QMutexLocker>
#include <QDebug>
#include <QFile>
#include <QDataStream>
#include <QFileInfo>

FileRelationshipCache::FileRelationshipCache(QObject* parent)
    : QObject(parent)
    , m_cacheValid(true)
    , m_lastUpdateTime(QDateTime::currentDateTime())
{
}

FileRelationshipCache::~FileRelationshipCache() {
    clearCache();
}

void FileRelationshipCache::addRelationship(const QString& parent, const QString& child) {
    if (parent.isEmpty() || child.isEmpty() || parent == child) {
        emit relationshipError("Invalid parent or child file name");
        return;
    }
    
    if (!isValidFileName(parent) || !isValidFileName(child)) {
        emit relationshipError("Invalid file name format");
        return;
    }
    
    if (hasCircularReference(parent, child)) {
        emit relationshipError("Circular reference detected");
        return;
    }
    
    QMutexLocker locker(&m_cacheMutex);
    
    try {
        updateRelationship(parent, child);
        markCacheUpdated();
        
        logRelationshipOperation("addRelationship", parent, child, true);
        emit relationshipAdded(parent, child);
        
    } catch (const std::exception& e) {
        emit relationshipError(QString("Failed to add relationship: %1").arg(e.what()));
        logRelationshipOperation("addRelationship", parent, child, false);
    }
}

void FileRelationshipCache::removeRelationship(const QString& parent, const QString& child) {
    if (parent.isEmpty() || child.isEmpty()) {
        emit relationshipError("Invalid parent or child file name");
        return;
    }
    
    QMutexLocker locker(&m_cacheMutex);
    
    try {
        removeRelationshipInternal(parent, child);
        markCacheUpdated();
        
        logRelationshipOperation("removeRelationship", parent, child, true);
        emit relationshipRemoved(parent, child);
        
    } catch (const std::exception& e) {
        emit relationshipError(QString("Failed to remove relationship: %1").arg(e.what()));
        logRelationshipOperation("removeRelationship", parent, child, false);
    }
}

void FileRelationshipCache::removeAllRelationships(const QString& parent) {
    if (parent.isEmpty()) {
        emit relationshipError("Invalid parent file name");
        return;
    }
    
    QMutexLocker locker(&m_cacheMutex);
    
    try {
        QStringList children = m_parentToChildren.value(parent);
        
        // 移除所有子项的父引用
        for (const QString& child : children) {
            m_childToParent.remove(child);
            emit relationshipRemoved(parent, child);
        }
        
        // 移除父项的所有子项
        m_parentToChildren.remove(parent);
        
        markCacheUpdated();
        logRelationshipOperation("removeAllRelationships", parent, QString(), true);
        
    } catch (const std::exception& e) {
        emit relationshipError(QString("Failed to remove all relationships: %1").arg(e.what()));
        logRelationshipOperation("removeAllRelationships", parent, QString(), false);
    }
}

QStringList FileRelationshipCache::getChildren(const QString& parent) const {
    QMutexLocker locker(&m_cacheMutex);
    return m_parentToChildren.value(parent, QStringList());
}

QString FileRelationshipCache::getParent(const QString& child) const {
    QMutexLocker locker(&m_cacheMutex);
    return m_childToParent.value(child, QString());
}

QStringList FileRelationshipCache::getAllParents() const {
    QMutexLocker locker(&m_cacheMutex);
    return m_parentToChildren.keys();
}

QStringList FileRelationshipCache::getAllChildren() const {
    QMutexLocker locker(&m_cacheMutex);
    return m_childToParent.keys();
}

QStringList FileRelationshipCache::getDescendants(const QString& ancestor) const {
    QMutexLocker locker(&m_cacheMutex);
    
    QStringList descendants;
    QStringList toProcess = m_parentToChildren.value(ancestor);
    
    while (!toProcess.isEmpty()) {
        QString current = toProcess.takeFirst();
        if (!descendants.contains(current)) {
            descendants.append(current);
            
            // 添加当前项的子项到处理队列
            QStringList children = m_parentToChildren.value(current);
            for (const QString& child : children) {
                if (!descendants.contains(child) && !toProcess.contains(child)) {
                    toProcess.append(child);
                }
            }
        }
    }
    
    return descendants;
}

QStringList FileRelationshipCache::getAncestors(const QString& descendant) const {
    QMutexLocker locker(&m_cacheMutex);
    
    QStringList ancestors;
    QString current = descendant;
    
    while (!current.isEmpty()) {
        QString parent = m_childToParent.value(current);
        if (parent.isEmpty() || ancestors.contains(parent)) {
            break; // 到达根节点或检测到循环
        }
        
        ancestors.append(parent);
        current = parent;
    }
    
    return ancestors;
}

int FileRelationshipCache::getDepth(const QString& fileName) const {
    return getAncestors(fileName).size();
}

QStringList FileRelationshipCache::getChildrenByType(const QString& parent, FileType fileType) const {
    QStringList children = getChildren(parent);
    QStringList filteredChildren;
    
    FileNameManager* nameManager = FileNameManager::getInstance();
    
    for (const QString& child : children) {
        if (nameManager->identifyFileType(child) == fileType) {
            filteredChildren.append(child);
        }
    }
    
    return filteredChildren;
}

QStringList FileRelationshipCache::getFilesByType(FileType fileType) const {
    QMutexLocker locker(&m_cacheMutex);
    
    QStringList allFiles;
    allFiles.append(m_parentToChildren.keys());
    allFiles.append(m_childToParent.keys());
    allFiles.removeDuplicates();
    
    QStringList filteredFiles;
    FileNameManager* nameManager = FileNameManager::getInstance();
    
    for (const QString& file : allFiles) {
        if (nameManager->identifyFileType(file) == fileType) {
            filteredFiles.append(file);
        }
    }
    
    return filteredFiles;
}

void FileRelationshipCache::refreshCache() {
    QMutexLocker locker(&m_cacheMutex);
    
    // 验证所有关系的有效性
    QStringList invalidParents;
    
    for (auto it = m_parentToChildren.begin(); it != m_parentToChildren.end(); ++it) {
        const QString& parent = it.key();
        const QStringList& children = it.value();
        
        // 检查父文件是否存在
        if (!isValidFileName(parent)) {
            invalidParents.append(parent);
            continue;
        }
        
        // 检查子文件的有效性
        QStringList validChildren;
        for (const QString& child : children) {
            if (isValidFileName(child)) {
                validChildren.append(child);
            } else {
                // 移除无效的子文件引用
                m_childToParent.remove(child);
            }
        }
        
        // 更新有效的子文件列表
        if (validChildren.size() != children.size()) {
            it.value() = validChildren;
        }
    }
    
    // 移除无效的父文件
    for (const QString& invalidParent : invalidParents) {
        removeAllRelationships(invalidParent);
    }
    
    markCacheUpdated();
    emit cacheRefreshed();
}

void FileRelationshipCache::clearCache() {
    QMutexLocker locker(&m_cacheMutex);
    
    m_parentToChildren.clear();
    m_childToParent.clear();
    m_cacheValid = false;
    
    emit cacheInvalidated();
}

bool FileRelationshipCache::isCacheValid() const {
    QMutexLocker locker(&m_cacheMutex);
    return m_cacheValid;
}

void FileRelationshipCache::invalidateCache() {
    QMutexLocker locker(&m_cacheMutex);
    m_cacheValid = false;
    emit cacheInvalidated();
}

int FileRelationshipCache::getRelationshipCount() const {
    QMutexLocker locker(&m_cacheMutex);
    return m_childToParent.size();
}

int FileRelationshipCache::getParentCount() const {
    QMutexLocker locker(&m_cacheMutex);
    return m_parentToChildren.size();
}

int FileRelationshipCache::getChildCount() const {
    QMutexLocker locker(&m_cacheMutex);
    return m_childToParent.size();
}

QDateTime FileRelationshipCache::getLastUpdateTime() const {
    QMutexLocker locker(&m_cacheMutex);
    return m_lastUpdateTime;
}

bool FileRelationshipCache::validateRelationships() const {
    QMutexLocker locker(&m_cacheMutex);
    
    // 检查数据一致性
    for (auto it = m_parentToChildren.begin(); it != m_parentToChildren.end(); ++it) {
        const QString& parent = it.key();
        const QStringList& children = it.value();
        
        for (const QString& child : children) {
            // 检查子项是否正确引用父项
            if (m_childToParent.value(child) != parent) {
                return false;
            }
        }
    }
    
    // 检查循环引用
    return findCircularReferences().isEmpty();
}

QStringList FileRelationshipCache::findOrphanedChildren() const {
    QMutexLocker locker(&m_cacheMutex);
    
    QStringList orphaned;
    
    for (auto it = m_childToParent.begin(); it != m_childToParent.end(); ++it) {
        const QString& child = it.key();
        const QString& parent = it.value();
        
        // 检查父项是否存在且包含此子项
        if (!m_parentToChildren.contains(parent) || 
            !m_parentToChildren[parent].contains(child)) {
            orphaned.append(child);
        }
    }
    
    return orphaned;
}

QStringList FileRelationshipCache::findCircularReferences() const {
    QMutexLocker locker(&m_cacheMutex);
    
    QStringList circular;
    
    for (auto it = m_parentToChildren.begin(); it != m_parentToChildren.end(); ++it) {
        const QString& parent = it.key();
        
        if (isDescendant(parent, parent)) {
            circular.append(parent);
        }
    }
    
    return circular;
}

// 私有方法实现
void FileRelationshipCache::updateRelationship(const QString& parent, const QString& child) {
    // 如果子项已有父项，先移除旧关系
    QString oldParent = m_childToParent.value(child);
    if (!oldParent.isEmpty() && oldParent != parent) {
        removeRelationshipInternal(oldParent, child);
    }

    // 添加新关系
    if (!m_parentToChildren[parent].contains(child)) {
        m_parentToChildren[parent].append(child);
    }
    m_childToParent[child] = parent;
}

void FileRelationshipCache::removeRelationshipInternal(const QString& parent, const QString& child) {
    // 从父项的子项列表中移除
    if (m_parentToChildren.contains(parent)) {
        m_parentToChildren[parent].removeAll(child);

        // 如果父项没有子项了，移除父项记录
        if (m_parentToChildren[parent].isEmpty()) {
            m_parentToChildren.remove(parent);
        }
    }

    // 移除子项的父项引用
    m_childToParent.remove(child);
}

bool FileRelationshipCache::isValidFileName(const QString& fileName) const {
    return UnifiedFileUtils::validateFileName(fileName);
}

bool FileRelationshipCache::hasCircularReference(const QString& parent, const QString& child) const {
    // 检查child是否是parent的祖先
    return isDescendant(child, parent);
}

bool FileRelationshipCache::isDescendant(const QString& ancestor, const QString& descendant) const {
    if (ancestor == descendant) {
        return true;
    }

    QStringList children = m_parentToChildren.value(ancestor);
    for (const QString& child : children) {
        if (isDescendant(child, descendant)) {
            return true;
        }
    }

    return false;
}

void FileRelationshipCache::markCacheUpdated() {
    m_lastUpdateTime = QDateTime::currentDateTime();
    m_cacheValid = true;
}

void FileRelationshipCache::logRelationshipOperation(const QString& operation,
                                                    const QString& parent,
                                                    const QString& child,
                                                    bool success) {
    if (success) {
        qDebug() << "Relationship operation succeeded:" << operation
                 << "parent:" << parent << "child:" << child;
    } else {
        qWarning() << "Relationship operation failed:" << operation
                   << "parent:" << parent << "child:" << child;
    }
}

// FileRelationshipItem 实现
QByteArray FileRelationshipItem::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    stream << parent << child << static_cast<int>(parentType)
           << static_cast<int>(childType) << createdTime;

    return data;
}

bool FileRelationshipItem::deserialize(const QByteArray& data) {
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    int parentTypeInt, childTypeInt;
    stream >> parent >> child >> parentTypeInt >> childTypeInt >> createdTime;

    parentType = static_cast<FileType>(parentTypeInt);
    childType = static_cast<FileType>(childTypeInt);

    return stream.status() == QDataStream::Ok && isValid();
}

bool FileRelationshipItem::isValid() const {
    return !parent.isEmpty() && !child.isEmpty() && parent != child;
}

// RelationshipStatistics 实现
QByteArray RelationshipStatistics::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    stream << totalRelationships << parentCount << childCount
           << orphanedChildren << circularReferences << lastUpdate;

    return data;
}

bool RelationshipStatistics::deserialize(const QByteArray& data) {
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    stream >> totalRelationships >> parentCount >> childCount
           >> orphanedChildren >> circularReferences >> lastUpdate;

    return stream.status() == QDataStream::Ok;
}
