#include "UnifiedFileStructures.h"
#include <QCryptographicHash>
#include <QDataStream>
#include <QtEndian>  // For qFromBigEndian
#include <QRegularExpression>
#include <QDebug>
#include <numeric>
#include <algorithm>
#include <QIODevice>

// OperationCounters implementation
int OperationCounters::getCounter(OperationType type) const {
    switch (type) {
    case OperationType::Alignment: return alignmentCounter;
    case OperationType::Crop: return cropCounter;
    case OperationType::AddCurve: return addCurveCounter;
    }
    return 0;
}

int OperationCounters::getCounter(AnalysisType type) const {
    switch (type) {
    case AnalysisType::DecayAnalysis: return decayAnalysisCounter;
    case AnalysisType::SpectralAnalysis: return spectralAnalysisCounter;
    case AnalysisType::FluorescenceAnalysis: return fluorescenceAnalysisCounter;
    }
    return 0;
}

void OperationCounters::incrementCounter(OperationType type) {
    switch (type) {
    case OperationType::Alignment: alignmentCounter++; break;
    case OperationType::Crop: cropCounter++; break;
    case OperationType::AddCurve: addCurveCounter++; break;
    }
}

void OperationCounters::incrementCounter(AnalysisType type) {
    switch (type) {
    case AnalysisType::DecayAnalysis: decayAnalysisCounter++; break;
    case AnalysisType::SpectralAnalysis: spectralAnalysisCounter++; break;
    case AnalysisType::FluorescenceAnalysis: fluorescenceAnalysisCounter++; break;
    }
}

void OperationCounters::setCounter(OperationType type, int value) {
    if (value < 0) return;
    switch (type) {
    case OperationType::Alignment: alignmentCounter = value; break;
    case OperationType::Crop: cropCounter = value; break;
    case OperationType::AddCurve: addCurveCounter = value; break;
    }
}

void OperationCounters::setCounter(AnalysisType type, int value) {
    if (value < 0) return;
    switch (type) {
    case AnalysisType::DecayAnalysis: decayAnalysisCounter = value; break;
    case AnalysisType::SpectralAnalysis: spectralAnalysisCounter = value; break;
    case AnalysisType::FluorescenceAnalysis: fluorescenceAnalysisCounter = value; break;
    }
}

QByteArray OperationCounters::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream << alignmentCounter << cropCounter << addCurveCounter
           << decayAnalysisCounter << spectralAnalysisCounter << fluorescenceAnalysisCounter;
    
    return data;
}

bool OperationCounters::deserialize(const QByteArray& data) {
    if (data.isEmpty()) return false;
    
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream >> alignmentCounter >> cropCounter >> addCurveCounter
           >> decayAnalysisCounter >> spectralAnalysisCounter >> fluorescenceAnalysisCounter;
    
    return stream.status() == QDataStream::Ok;
}

void OperationCounters::reset() {
    alignmentCounter = cropCounter = addCurveCounter = 0;
    decayAnalysisCounter = spectralAnalysisCounter = fluorescenceAnalysisCounter = 0;
}

// OperationSequence implementation
QString OperationSequence::toString() const {
    QStringList parts;
    for (const auto& op : operations) {
        QString typeStr = UnifiedFileUtils::operationTypeToString(op.first);
        parts << QString("%1%2").arg(typeStr).arg(op.second);
    }
    return parts.join("_");
}

OperationSequence OperationSequence::fromString(const QString& sequence) {
    OperationSequence result;
    if (sequence.isEmpty()) return result;
    
    QStringList parts = sequence.split("_", Qt::SkipEmptyParts);
    for (const QString& part : parts) {
        if (part.length() < 3) continue;
        
        QString typeStr = part.left(2);
        QString numStr = part.mid(2);
        
        OperationType type = UnifiedFileUtils::stringToOperationType(typeStr);
        bool ok;
        int num = numStr.toInt(&ok);
        
        if (ok && num > 0) {
            result.operations.append(qMakePair(type, num));
        }
    }
    
    return result;
}

void OperationSequence::appendOperation(OperationType type, int sequence) {
    if (sequence > 0) {
        operations.append(qMakePair(type, sequence));
    }
}

bool OperationSequence::isEmpty() const {
    return operations.isEmpty();
}

void OperationSequence::clear() {
    operations.clear();
}

QByteArray OperationSequence::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream << operations.size();
    for (const auto& op : operations) {
        stream << static_cast<int>(op.first) << op.second;
    }
    
    return data;
}

bool OperationSequence::deserialize(const QByteArray& data) {
    if (data.isEmpty()) return false;
    
    operations.clear();
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    int size;
    stream >> size;
    
    for (int i = 0; i < size; ++i) {
        int typeInt, sequence;
        stream >> typeInt >> sequence;
        operations.append(qMakePair(static_cast<OperationType>(typeInt), sequence));
    }
    
    return stream.status() == QDataStream::Ok;
}

// PlotDataCollection implementation
QByteArray PlotDataCollection::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream << fluorescenceMapData << decayCurveData << spectralCurveData
           << totalCountsData << metadata;
    
    return data;
}

bool PlotDataCollection::deserialize(const QByteArray& data) {
    if (data.isEmpty()) return false;
    
    clear();
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream >> fluorescenceMapData >> decayCurveData >> spectralCurveData
           >> totalCountsData >> metadata;
    
    return stream.status() == QDataStream::Ok;
}

bool PlotDataCollection::isValid() const {
    return !fluorescenceMapData.isEmpty() || !decayCurveData.isEmpty() || 
           !spectralCurveData.isEmpty() || !totalCountsData.isEmpty();
}

qint64 PlotDataCollection::calculateSize() const {
    return fluorescenceMapData.size() + totalCountsData.size() +
           std::accumulate(decayCurveData.begin(), decayCurveData.end(), 0LL,
                          [](qint64 sum, const QByteArray& data) { return sum + data.size(); }) +
           std::accumulate(spectralCurveData.begin(), spectralCurveData.end(), 0LL,
                          [](qint64 sum, const QByteArray& data) { return sum + data.size(); });
}

void PlotDataCollection::clear() {
    fluorescenceMapData.clear();
    decayCurveData.clear();
    spectralCurveData.clear();
    totalCountsData.clear();
    metadata.clear();
}

// OperationMetadata implementation
QByteArray OperationMetadata::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream << static_cast<int>(operationType) << timestamp << operationName
           << sequence.serialize() << parameters << description;
    
    return data;
}

bool OperationMetadata::deserialize(const QByteArray& data) {
    if (data.isEmpty()) return false;
    
    clear();
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    int typeInt;
    QByteArray sequenceData;
    stream >> typeInt >> timestamp >> operationName >> sequenceData >> parameters >> description;
    
    operationType = static_cast<OperationType>(typeInt);
    sequence.deserialize(sequenceData);
    
    return stream.status() == QDataStream::Ok;
}

bool OperationMetadata::isValid() const {
    return !operationName.isEmpty() && timestamp.isValid();
}

void OperationMetadata::clear() {
    operationName.clear();
    description.clear();
    parameters.clear();
    sequence.clear();
    timestamp = QDateTime();
}

// AnalysisResults implementation
QByteArray AnalysisResults::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream << static_cast<int>(analysisType) << parameters << fittedCurve
           << residualCurve << analysisReport << chiSquared << rSquared
           << timestamp << additionalData;
    
    return data;
}

bool AnalysisResults::deserialize(const QByteArray& data) {
    if (data.isEmpty()) return false;
    
    clear();
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    int typeInt;
    stream >> typeInt >> parameters >> fittedCurve >> residualCurve
           >> analysisReport >> chiSquared >> rSquared >> timestamp >> additionalData;
    
    analysisType = static_cast<AnalysisType>(typeInt);
    
    return stream.status() == QDataStream::Ok;
}

bool AnalysisResults::isValid() const {
    return timestamp.isValid() && (!fittedCurve.isEmpty() || !parameters.isEmpty());
}

void AnalysisResults::clear() {
    parameters.clear();
    fittedCurve.clear();
    residualCurve.clear();
    analysisReport.clear();
    additionalData.clear();
    chiSquared = rSquared = 0.0;
    timestamp = QDateTime();
}

// AnalysisParameters implementation
QByteArray AnalysisParameters::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream << modelType << initialValues << limits << fixedParameters << additionalSettings;
    
    return data;
}

bool AnalysisParameters::deserialize(const QByteArray& data) {
    if (data.isEmpty()) return false;
    
    clear();
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);
    
    stream >> modelType >> initialValues >> limits >> fixedParameters >> additionalSettings;
    
    return stream.status() == QDataStream::Ok;
}

bool AnalysisParameters::isValid() const {
    return !modelType.isEmpty();
}

void AnalysisParameters::clear() {
    modelType.clear();
    initialValues.clear();
    limits.clear();
    fixedParameters.clear();
    additionalSettings.clear();
}

// SflpFileHeader implementation
bool SflpFileHeader::isValid() const {
    return magicNumber == 0x534C4650 && version > 0 && headerSize > 0;
}

QByteArray SflpFileHeader::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    stream << magicNumber << version << headerSize << indexOffset << indexSize
           << dataSegmentCount << totalFileSize << compressionType;

    for (int i = 0; i < 16; ++i) {
        stream << reserved[i];
    }

    return data;
}

bool SflpFileHeader::deserialize(const QByteArray& data) {
    if (data.size() < static_cast<int>(sizeof(SflpFileHeader))) return false;

    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    stream >> magicNumber >> version >> headerSize >> indexOffset >> indexSize
           >> dataSegmentCount >> totalFileSize >> compressionType;

    for (int i = 0; i < 16; ++i) {
        stream >> reserved[i];
    }

    return stream.status() == QDataStream::Ok && isValid();
}

void SflpFileHeader::reset() {
    magicNumber = 0x534C4650;
    version = 1;
    headerSize = sizeof(SflpFileHeader);
    indexOffset = indexSize = dataSegmentCount = 0;
    totalFileSize = 0;
    compressionType = 1;
    for (int i = 0; i < 16; ++i) {
        reserved[i] = 0;
    }
}

// DataSegmentIndex implementation
QByteArray DataSegmentIndex::serialize() const {
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    stream << segmentName << offset << compressedSize << originalSize
           << checksum << timestamp;

    return data;
}

bool DataSegmentIndex::deserialize(const QByteArray& data) {
    if (data.isEmpty()) return false;

    clear();
    QDataStream stream(data);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    stream >> segmentName >> offset >> compressedSize >> originalSize
           >> checksum >> timestamp;

    return stream.status() == QDataStream::Ok;
}

bool DataSegmentIndex::isValid() const {
    return !segmentName.isEmpty() && compressedSize > 0 && originalSize > 0;
}

void DataSegmentIndex::clear() {
    segmentName.clear();
    offset = compressedSize = originalSize = 0;
    checksum = 0;
    timestamp = QDateTime();
}

// UnifiedFileUtils implementation
QString UnifiedFileUtils::operationTypeToString(OperationType type) {
    switch (type) {
    case OperationType::Alignment: return "al";
    case OperationType::Crop: return "cr";
    case OperationType::AddCurve: return "ad";
    }
    return "unknown";
}

OperationType UnifiedFileUtils::stringToOperationType(const QString& str) {
    if (str == "al") return OperationType::Alignment;
    if (str == "cr") return OperationType::Crop;
    if (str == "ad") return OperationType::AddCurve;
    return OperationType::Alignment; // default
}

QString UnifiedFileUtils::analysisTypeToString(AnalysisType type) {
    switch (type) {
    case AnalysisType::DecayAnalysis: return "DecAna";
    case AnalysisType::SpectralAnalysis: return "SpeAna";
    case AnalysisType::FluorescenceAnalysis: return "FluAna";
    }
    return "unknown";
}

AnalysisType UnifiedFileUtils::stringToAnalysisType(const QString& str) {
    if (str == "DecAna") return AnalysisType::DecayAnalysis;
    if (str == "SpeAna") return AnalysisType::SpectralAnalysis;
    if (str == "FluAna") return AnalysisType::FluorescenceAnalysis;
    return AnalysisType::DecayAnalysis; // default
}

QString UnifiedFileUtils::fileTypeToString(FileType type) {
    switch (type) {
    case FileType::Workspace: return "Workspace";
    case FileType::Project: return "Project";
    case FileType::Operation: return "Operation";
    case FileType::DecayAnalysis: return "DecayAnalysis";
    case FileType::SpectralAnalysis: return "SpectralAnalysis";
    case FileType::FluorescenceAnalysis: return "FluorescenceAnalysis";
    case FileType::Split: return "Split";
    }
    return "unknown";
}

FileType UnifiedFileUtils::stringToFileType(const QString& str) {
    if (str == "Workspace") return FileType::Workspace;
    if (str == "Project") return FileType::Project;
    if (str == "Operation") return FileType::Operation;
    if (str == "DecayAnalysis") return FileType::DecayAnalysis;
    if (str == "SpectralAnalysis") return FileType::SpectralAnalysis;
    if (str == "FluorescenceAnalysis") return FileType::FluorescenceAnalysis;
    if (str == "Split") return FileType::Split;
    return FileType::Project; // default
}

quint32 UnifiedFileUtils::calculateChecksum(const QByteArray& data) {
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(data);
    QByteArray result = hash.result();

    quint32 checksum = 0;
    if (result.size() >= 4) {
        checksum = qFromBigEndian<quint32>(reinterpret_cast<const uchar*>(result.constData()));
    }

    return checksum;
}

bool UnifiedFileUtils::validateFileName(const QString& fileName) {
    if (fileName.isEmpty() || fileName.length() > 255) {
        return false;
    }

    // 检查非法字符
    QRegularExpression invalidChars("[<>:\"/\\\\|?*]");
    return !invalidChars.match(fileName).hasMatch();
}

QString UnifiedFileUtils::sanitizeFileName(const QString& fileName) {
    QString sanitized = fileName;

    // 替换非法字符
    QRegularExpression invalidChars("[<>:\"/\\\\|?*]");
    sanitized.replace(invalidChars, "_");

    // 限制长度
    if (sanitized.length() > 255) {
        sanitized = sanitized.left(255);
    }

    return sanitized;
}
