#include "SflpFileManager.h"
#include <QFileInfo>
#include <QDir>
#include <QMutexLocker>
#include <QDebug>
#include <QDateTime>

SflpFileManager::SflpFileManager(const QString& fileName, QObject* parent)
    : QObject(parent)
    , m_fileName(fileName)
    , m_file(nullptr)
    , m_isModified(false)
{
    m_file = new QFile(m_fileName, this);
    m_header.reset();
}

SflpFileManager::~SflpFileManager() {
    closeFile();
}

bool SflpFileManager::openFile(QIODevice::OpenMode mode) {
    if (m_file->isOpen()) {
        closeFile();
    }
    
    clearError();
    
    // 确保目录存在
    QFileInfo fileInfo(m_fileName);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            setError(QString("Failed to create directory: %1").arg(dir.absolutePath()));
            return false;
        }
    }
    
    // 如果文件不存在且是写模式，创建新文件
    if (!m_file->exists() && (mode & QIODevice::WriteOnly)) {
        if (!createEmptyFile(m_fileName)) {
            setError(QString("Failed to create new SFLP file: %1").arg(m_fileName));
            return false;
        }
    }
    
    if (!m_file->open(mode)) {
        setError(QString("Failed to open file: %1 - %2").arg(m_fileName, m_file->errorString()));
        return false;
    }
    
    // 读取文件头和索引
    if (m_file->size() > 0) {
        if (!readHeader()) {
            closeFile();
            return false;
        }
        
        if (!readIndex()) {
            closeFile();
            return false;
        }
    } else {
        // 新文件，写入默认头部
        m_header.reset();
        if (!writeHeader()) {
            closeFile();
            return false;
        }
    }
    
    return true;
}

void SflpFileManager::closeFile() {
    
    if (m_file && m_file->isOpen()) {
        if (m_isModified) {
            writeHeader();
            writeIndex();
            m_isModified = false;
        }
        m_file->close();
    }
}

bool SflpFileManager::isOpen() const {
    return m_file && m_file->isOpen();
}

bool SflpFileManager::exists() const {
    return QFile::exists(m_fileName);
}

bool SflpFileManager::writeDataSegment(const QString& segmentName, const QByteArray& data) {
    if (segmentName.isEmpty() || data.isEmpty()) {
        setError("Invalid segment name or empty data");
        return false;
    }
    
    return writeDataSegmentInternal(segmentName, data, false);
}

QByteArray SflpFileManager::readDataSegment(const QString& segmentName) {
    if (segmentName.isEmpty()) {
        setError("Invalid segment name");
        return QByteArray();
    }
    
    return readDataSegmentInternal(segmentName);
}

bool SflpFileManager::removeDataSegment(const QString& segmentName) {
    
    if (!isOpen()) {
        setError("File is not open");
        return false;
    }
    
    if (!m_indexMap.contains(segmentName)) {
        setError(QString("Data segment not found: %1").arg(segmentName));
        return false;
    }
    
    removeDataSegmentIndex(segmentName);
    m_header.dataSegmentCount = m_indexMap.size();
    m_isModified = true;
    
    emit dataSegmentRemoved(segmentName);
    return true;
}

QStringList SflpFileManager::getDataSegmentNames() const {
    return m_indexMap.keys();
}

bool SflpFileManager::hasDataSegment(const QString& segmentName) const {
    return m_indexMap.contains(segmentName);
}

bool SflpFileManager::writeCompressedDataSegment(const QString& segmentName, const QByteArray& rawData) {
    if (segmentName.isEmpty() || rawData.isEmpty()) {
        setError("Invalid segment name or empty data");
        return false;
    }
    
    QByteArray compressedData = compressData(rawData);
    if (compressedData.isEmpty()) {
        setError("Failed to compress data");
        return false;
    }
    
    return writeDataSegmentInternal(segmentName, compressedData, true);
}

QByteArray SflpFileManager::readCompressedDataSegment(const QString& segmentName) {
    QByteArray compressedData = readDataSegmentInternal(segmentName);
    if (compressedData.isEmpty()) {
        return QByteArray();
    }
    
    QByteArray rawData = decompressData(compressedData);
    if (rawData.isEmpty()) {
        setError("Failed to decompress data");
        return QByteArray();
    }
    
    return rawData;
}

bool SflpFileManager::rebuildIndex() {
    
    if (!isOpen()) {
        setError("File is not open");
        return false;
    }
    
    // 重建索引逻辑
    m_indexMap.clear();
    
    // 扫描文件，重建数据段索引
    qint64 currentPos = m_header.headerSize;
    qint64 fileSize = m_file->size();
    
    while (currentPos < fileSize) {
        m_file->seek(currentPos);
        
        // 读取数据段头部信息
        DataSegmentIndex segmentIndex;
        QByteArray indexData = m_file->read(256); // 假设索引数据最大256字节
        
        if (!segmentIndex.deserialize(indexData)) {
            break;
        }
        
        if (segmentIndex.isValid()) {
            m_indexMap[segmentIndex.segmentName] = segmentIndex;
            currentPos = segmentIndex.offset + segmentIndex.compressedSize;
        } else {
            break;
        }
    }
    
    m_header.dataSegmentCount = m_indexMap.size();
    m_isModified = true;
    
    emit indexRebuilt();
    return true;
}

bool SflpFileManager::validateFile() const {
    
    if (!isOpen()) {
        return false;
    }
    
    // 验证文件头
    if (!m_header.isValid()) {
        return false;
    }
    
    // 验证所有数据段
    for (auto it = m_indexMap.begin(); it != m_indexMap.end(); ++it) {
        if (!validateDataSegment(it.key())) {
            return false;
        }
    }
    
    return true;
}

bool SflpFileManager::validateDataSegment(const QString& segmentName) const {
    
    if (!m_indexMap.contains(segmentName)) {
        return false;
    }
    
    const DataSegmentIndex& index = m_indexMap[segmentName];
    
    // 验证偏移和大小
    if (index.offset < m_header.headerSize || 
        index.offset + index.compressedSize > m_file->size()) {
        return false;
    }
    
    // 验证校验和
    m_file->seek(index.offset);
    QByteArray data = m_file->read(index.compressedSize);
    
    return verifyDataChecksum(segmentName, data);
}

qint64 SflpFileManager::getFileSize() const {
    return m_file ? m_file->size() : 0;
}

qint64 SflpFileManager::getDataSegmentSize(const QString& segmentName) const {
    
    if (m_indexMap.contains(segmentName)) {
        return m_indexMap[segmentName].originalSize;
    }
    
    return 0;
}

SflpFileHeader SflpFileManager::getFileHeader() const {
    return m_header;
}

QMap<QString, DataSegmentIndex> SflpFileManager::getDataSegmentIndex() const {
    return m_indexMap;
}

int SflpFileManager::getDataSegmentCount() const {
    return m_indexMap.size();
}

QString SflpFileManager::getLastError() const {
    return m_lastError;
}

bool SflpFileManager::hasError() const {
    return !m_lastError.isEmpty();
}

void SflpFileManager::clearError() {
    m_lastError.clear();
}

// 静态方法实现
bool SflpFileManager::createEmptyFile(const QString& fileName) {
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }
    
    SflpFileHeader header;
    header.reset();
    
    QByteArray headerData = header.serialize();
    qint64 written = file.write(headerData);
    file.close();
    
    return written == headerData.size();
}

bool SflpFileManager::isValidSflpFile(const QString& fileName) {
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }
    
    if (file.size() < static_cast<qint64>(sizeof(SflpFileHeader))) {
        return false;
    }
    
    QByteArray headerData = file.read(sizeof(SflpFileHeader));
    file.close();
    
    SflpFileHeader header;
    return header.deserialize(headerData);
}

QByteArray SflpFileManager::compressData(const QByteArray& data) {
    if (data.isEmpty()) {
        return QByteArray();
    }
    
    return qCompress(data);
}

QByteArray SflpFileManager::decompressData(const QByteArray& compressedData) {
    if (compressedData.isEmpty()) {
        return QByteArray();
    }
    
    return qUncompress(compressedData);
}

// 私有方法实现
bool SflpFileManager::readHeader() {
    if (!m_file || !m_file->isOpen()) {
        setError("File is not open");
        return false;
    }

    m_file->seek(0);
    QByteArray headerData = m_file->read(sizeof(SflpFileHeader));

    if (headerData.size() < static_cast<int>(sizeof(SflpFileHeader))) {
        setError("Failed to read file header");
        return false;
    }

    if (!m_header.deserialize(headerData)) {
        setError("Invalid file header");
        return false;
    }

    return true;
}

bool SflpFileManager::writeHeader() {
    if (!m_file || !m_file->isOpen()) {
        setError("File is not open");
        return false;
    }

    m_header.totalFileSize = m_file->size();
    m_header.indexOffset = calculateIndexOffset();

    QByteArray headerData = m_header.serialize();

    m_file->seek(0);
    qint64 written = m_file->write(headerData);

    if (written != headerData.size()) {
        setError("Failed to write file header");
        return false;
    }

    m_file->flush();
    return true;
}

bool SflpFileManager::readIndex() {
    if (m_header.indexOffset == 0 || m_header.indexSize == 0) {
        // 没有索引数据
        return true;
    }

    m_file->seek(m_header.indexOffset);
    QByteArray indexData = m_file->read(m_header.indexSize);

    if (indexData.size() != static_cast<int>(m_header.indexSize)) {
        setError("Failed to read index data");
        return false;
    }

    // 反序列化索引数据
    QDataStream stream(indexData);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    int segmentCount;
    stream >> segmentCount;

    m_indexMap.clear();
    for (int i = 0; i < segmentCount; ++i) {
        DataSegmentIndex index;
        QByteArray segmentIndexData;
        stream >> segmentIndexData;

        if (index.deserialize(segmentIndexData)) {
            m_indexMap[index.segmentName] = index;
        }
    }

    return stream.status() == QDataStream::Ok;
}

bool SflpFileManager::writeIndex() {
    QByteArray indexData;
    QDataStream stream(&indexData, QIODevice::WriteOnly);
    stream.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    stream << m_indexMap.size();
    for (auto it = m_indexMap.begin(); it != m_indexMap.end(); ++it) {
        QByteArray segmentIndexData = it.value().serialize();
        stream << segmentIndexData;
    }

    // 计算索引位置
    qint64 indexOffset = calculateIndexOffset();

    // 确保文件足够大
    if (m_file->size() < indexOffset + indexData.size()) {
        m_file->resize(indexOffset + indexData.size());
    }

    m_file->seek(indexOffset);
    qint64 written = m_file->write(indexData);

    if (written != indexData.size()) {
        setError("Failed to write index data");
        return false;
    }

    // 更新头部信息
    m_header.indexOffset = indexOffset;
    m_header.indexSize = indexData.size();
    m_header.dataSegmentCount = m_indexMap.size();

    m_file->flush();
    return true;
}

bool SflpFileManager::updateFileSize() {
    if (!m_file || !m_file->isOpen()) {
        return false;
    }

    m_header.totalFileSize = m_file->size();
    return true;
}

bool SflpFileManager::writeDataSegmentInternal(const QString& segmentName,
                                              const QByteArray& data,
                                              bool compressed) {
    if (!isOpen()) {
        setError("File is not open");
        return false;
    }

    // 查找空闲空间
    qint64 offset = findFreeSpace(data.size());
    if (offset < 0) {
        setError("No free space available");
        return false;
    }

    // 写入数据
    m_file->seek(offset);
    qint64 written = m_file->write(data);

    if (written != data.size()) {
        setError("Failed to write data segment");
        return false;
    }

    // 计算校验和
    quint32 checksum = calculateDataChecksum(data);

    // 更新索引
    qint64 originalSize = compressed ? 0 : data.size(); // 如果是压缩数据，原始大小需要另外提供
    updateDataSegmentIndex(segmentName, offset, data.size(), originalSize, checksum);

    m_header.dataSegmentCount = m_indexMap.size();
    m_isModified = true;

    emit dataSegmentWritten(segmentName, data.size());
    return true;
}

QByteArray SflpFileManager::readDataSegmentInternal(const QString& segmentName) {

    if (!isOpen()) {
        setError("File is not open");
        return QByteArray();
    }

    if (!m_indexMap.contains(segmentName)) {
        setError(QString("Data segment not found: %1").arg(segmentName));
        return QByteArray();
    }

    const DataSegmentIndex& index = m_indexMap[segmentName];

    m_file->seek(index.offset);
    QByteArray data = m_file->read(index.compressedSize);

    if (data.size() != static_cast<int>(index.compressedSize)) {
        setError("Failed to read complete data segment");
        return QByteArray();
    }

    // 验证校验和
    if (!verifyDataChecksum(segmentName, data)) {
        setError("Data segment checksum verification failed");
        return QByteArray();
    }

    return data;
}

qint64 SflpFileManager::findFreeSpace(qint64 requiredSize) {
    // 简化实现：总是在文件末尾添加
    qint64 fileSize = m_file->size();
    qint64 indexSpace = m_header.indexSize > 0 ? m_header.indexSize + 1024 : 1024; // 为索引预留空间

    return fileSize + indexSpace;
}

bool SflpFileManager::compactFile() {
    // 文件压缩整理实现（可选）
    // 这里提供简化版本
    return true;
}

quint32 SflpFileManager::calculateDataChecksum(const QByteArray& data) const {
    return UnifiedFileUtils::calculateChecksum(data);
}

bool SflpFileManager::verifyDataChecksum(const QString& segmentName, const QByteArray& data) const {
    if (!m_indexMap.contains(segmentName)) {
        return false;
    }

    quint32 expectedChecksum = m_indexMap[segmentName].checksum;
    quint32 actualChecksum = calculateDataChecksum(data);

    return expectedChecksum == actualChecksum;
}

void SflpFileManager::setError(const QString& error) {
    m_lastError = error;
    qWarning() << "SflpFileManager error:" << error;
    emit errorOccurred(error);
}

bool SflpFileManager::checkFileIntegrity() const {
    return validateFile();
}

void SflpFileManager::updateDataSegmentIndex(const QString& segmentName,
                                            qint64 offset,
                                            qint64 compressedSize,
                                            qint64 originalSize,
                                            quint32 checksum) {
    DataSegmentIndex index;
    index.segmentName = segmentName;
    index.offset = offset;
    index.compressedSize = compressedSize;
    index.originalSize = originalSize > 0 ? originalSize : compressedSize;
    index.checksum = checksum;
    index.timestamp = QDateTime::currentDateTime();

    m_indexMap[segmentName] = index;
}

void SflpFileManager::removeDataSegmentIndex(const QString& segmentName) {
    m_indexMap.remove(segmentName);
}

qint64 SflpFileManager::calculateIndexOffset() const {
    qint64 maxOffset = m_header.headerSize;

    // 找到所有数据段的最大结束位置
    for (auto it = m_indexMap.begin(); it != m_indexMap.end(); ++it) {
        qint64 endOffset = it.value().offset + it.value().compressedSize;
        maxOffset = qMax(maxOffset, endOffset);
    }

    return maxOffset;
}

bool SflpFileManager::lockFile() {
    // 文件锁定实现（可选）
    return true;
}

void SflpFileManager::unlockFile() {
    // 文件解锁实现（可选）
}

void SflpFileManager::handleFileError() {
    if (m_file && m_file->error() != QFile::NoError) {
        setError(m_file->errorString());
    }
}
