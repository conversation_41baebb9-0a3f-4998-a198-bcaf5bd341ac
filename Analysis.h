#pragma once

#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QTreeWidget>
#include <QGroupBox>
#include "BaseTab.h"
#include "analysis/FitCurveDisplay.h"
#include "analysis/FitParameterModel.h"
#include "analysis/FitUIBinder.h"
#include "SharedDataManager.h"


class AnalysisTab : public BaseTab {
    Q_OBJECT

public:
    explicit AnalysisTab(QWidget *parent = nullptr);
    ~AnalysisTab();


private slots:
    // 拟合相关的槽函数
    void onClearFitsClicked();

    // 从参数配置执行拟合（尾部拟合）
    void onFitFromParametersClicked();

    // 执行卷积拟合
    void onConvolutionFitClicked();

    // 保存按钮点击处理函数
    void onSaveClicked();

    // 分析保存相关的辅助方法
    bool hasAnalysisData() const;
    AnalysisType getCurrentAnalysisType() const;
    void collectAnalysisParameters(AnalysisResults& results) const;
    void collectFittingResults(AnalysisResults& results) const;
    void collectAnalysisReport(AnalysisResults& results) const;
    QString generateAnalysisName(AnalysisType type) const;

    // 数据转换辅助方法
    QVector<QPointF> convertGraphDataToPointF(const QVector<GraphData>& graphData) const;
    QMap<QString, double> convertFitParametersToMap(const QVector<FitParameters>& fitParams) const;

    // 处理数据同步完成信号
    void onDataSynced(TabType sourceTab, TabType targetTab, PlotDataType plotType);

    // 处理Fitting Range相关的槽函数
    void onCheckRangeStateChanged(int state);
    void onRangeFromValueChanged(double value);
    void onRangeToValueChanged(double value);

private:
    // UI Components
    QComboBox* analysisMethodComboBox;

    // 拟合相关组件
    FitCurveDisplay* m_decayCurveFitDisplay = nullptr;
    FitCurveDisplay* m_spectralCurveFitDisplay = nullptr;
    QPushButton* m_fitDecayCurveButton = nullptr;
    QPushButton* m_fitSpectralCurveButton = nullptr;
    QPushButton* m_clearFitsButton = nullptr;
    QPushButton* m_fitButton = nullptr; // 直接从参数执行拟合的按钮
    QPushButton* m_saveButton = nullptr; // 保存按钮

    // 参数配置相关组件
    QComboBox* m_fittingModelComboBox = nullptr;
    QComboBox* m_fittingModelComboBox_1 = nullptr;
    QDoubleSpinBox* m_t0SpinBox = nullptr;
    QDoubleSpinBox* m_fwhmSpinBox = nullptr;
    QComboBox* m_analysisModeComboBox = nullptr;
    QComboBox* m_exponentialModelComboBox = nullptr;
    QSpinBox* m_modelParamsSpinBox = nullptr;
    QComboBox* m_modelAlgorithmComboBox = nullptr;
    QSpinBox* m_iterationsSpinBox = nullptr;
    QCheckBox* checkrange = nullptr;
    QDoubleSpinBox* m_rangeFromSpinBox = nullptr;
    QDoubleSpinBox* m_rangeToSpinBox = nullptr;
    QTableWidget* m_parameterTable = nullptr;

    // UI绑定器
    FitUIBinder* m_fitUIBinder = nullptr;

    // UI Components specific to AnalysisTab
    QPushButton* decayCurveButton = nullptr;
    QPushButton* spectralCurveButton = nullptr;
    QPushButton* fluorescenceMapButton = nullptr;

    // Results display
    QPlainTextEdit* m_fitResultsTextEdit = nullptr;


    // Widget Creation
    QWidget* createSpectralCurvePageWidget();
    QWidget* createFluorescenceMapPageWidget();
    QWidget* createDecayCurvePage();

    // UI Setup Methods
    void setupAnalysisMethodSection(QVBoxLayout* mainLayout);
    void setupFittingModelSection(QVBoxLayout* groupLayout);
    void setupIRFSection(QVBoxLayout* groupLayout);
    void setupAnalysisModeSection(QVBoxLayout* groupLayout);
    void setupExponentialModelSection(QVBoxLayout* groupLayout);
    void setupModelParametersSection(QVBoxLayout* groupLayout);
    void setupModelAlgorithmSection(QVBoxLayout* groupLayout);
    void setupIterationsSection(QVBoxLayout* groupLayout);
    void setupFittingRangeSection(QVBoxLayout* groupLayout);
    void setupParameterTable(QVBoxLayout* mainLayout);
    void setupActionButtons(QVBoxLayout* mainLayout);

    // Helper Methods
    QHBoxLayout* createLabelComboRow(const QString& labelText, QComboBox*& comboBox);
    QHBoxLayout* createLabelSpinBoxRow(const QString& labelText, QSpinBox*& spinBox);
    QHBoxLayout* createLabelDoubleSpinBoxRow(const QString& labelText, QDoubleSpinBox*& spinBox);

    void setupParameterRow(QTableWidget* table, int row, const QString& label);

    // Plot setup
    void setupPlots() override;

    // 文件选择相关的槽函数
    void onFileSelected(int row, const QString &filePath);

    // Fitting Range垂直线
    QCPItemLine* m_rangeFromLine = nullptr;
    QCPItemLine* m_rangeToLine = nullptr;

    // 更新垂直线位置
    void updateRangeLines();

    // 全局变量plot4，用于显示残差图
    CustomizePlot* plot4 = nullptr;

    // 计算残差并在plot4中显示
    void calculateAndPlotResiduals(const QVector<double>& xData, const QVector<double>& originalY,
                                  const QVector<double>& fittedY, bool isTailFitting);
};
